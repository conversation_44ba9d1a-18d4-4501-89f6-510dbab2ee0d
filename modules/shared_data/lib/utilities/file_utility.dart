import 'dart:async';
import 'dart:io';
import 'dart:typed_data';

import 'package:dartz/dartz.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart' show PlatformException, rootBundle;
import 'package:flutter_video_info/flutter_video_info.dart';
import 'package:icloud_storage/icloud_storage.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:image_size_getter/file_input.dart';
import 'package:image_size_getter/image_size_getter.dart';
import 'package:image_size_getter_http_input/image_size_getter_http_input.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:promise/promise.dart';
import 'package:shared_data/shared_data.dart';
import 'package:video_compress/video_compress.dart';

class FileUtility {
  static int ONE_MB = 1024 * 1024;

  static Future<String> getFileJson(String jsonFilePath) {
    return rootBundle.loadString(jsonFilePath);
  }

  static Future<String> getDocumentFilePath(String fileName) async {
    final directory = await getApplicationDocumentsDirectory();
    final file = File('${directory.path}/$fileName');
    final fileDirectory = file.parent;
    if (!fileDirectory.existsSync()) {
      fileDirectory.createSync(recursive: true);
    }

    return file.path;
  }

  static Future<String> getExternalStorageFilePath(String fileName) async {
    final directory = await getExternalStorageDirectory();
    final file = File('${directory?.path}/$fileName');
    final fileDirectory = file.parent;
    if (!fileDirectory.existsSync()) {
      fileDirectory.createSync(recursive: true);
    }

    return file.path;
  }

  static bool isFileExisted(String? filePath) {
    if (StringUtility.isEmpty(filePath)) return false;

    try {
      return File(unwrap(filePath, '')).existsSync();
    } catch (e) {
      debugPrint('isFileExisted $e');
    }
    return false;
  }

  static void deleteFile(String filePath) {
    try {
      return File(filePath).deleteSync();
    } catch (e) {
      debugPrint('deleteFile $e');
    }
  }

  static Future<void> writeFile(String filePath, String text) async {
    try {
      final file = File(filePath);
      if (!file.existsSync()) {
        file.createSync(recursive: true);
      }
      await file.writeAsString(text);
    } catch (e) {
      debugPrint('writeFile $e');
    }
    return;
  }

  static Future<String?> readFile(String filePath) async {
    try {
      final file = File(filePath);
      return await file.readAsString();
    } catch (e) {
      debugPrint('readFile $e');
    }
    return null;
  }

  static Future<void> printICloudFile(String iCloudContainerId) async {
    final fileList = await ICloudStorage.gather(containerId: iCloudContainerId);
    debugPrint('====List files in iCloud Container====');
    for (final file in fileList) {
      debugPrint('=> File: ${file.relativePath}');
    }
    debugPrint('=====================================');
    return;
  }

  static Future<void> deleteAllICloudFile(String iCloudContainerId) async {
    final fileList = await ICloudStorage.gather(containerId: iCloudContainerId);
    debugPrint('====List files in iCloud Container====');
    for (final file in fileList) {
      debugPrint('=> File Delete: ${file.relativePath}');
      await deleteFileOnICloudContainer(
          iCloudContainerId, Uri.decodeFull(file.relativePath));
    }
    debugPrint('=====================================');
    return;
  }

  static Future<Either<Exception, String?>>
      getFileDownloadedFromICloudContainer(
    String iCloudContainerId,
    String iCloudFilePath,
    String localFilePath,
  ) async {
    try {
      final filePath = await Promise.run(
        (resolve, reject) async {
          var fileList = <ICloudFile>[];
          try {
            fileList =
                await ICloudStorage.gather(containerId: iCloudContainerId);
          } on Exception catch (exception) {
            reject.call(exception);
            return;
          }
          if (ListUtility.isEmpty(fileList)) {
            resolve.call(null);
            return;
          }
          var hasFile = false;
          for (var file in fileList) {
            if (file.relativePath.endsWith(iCloudFilePath)) {
              hasFile = true;
              StreamSubscription? downloadProgressSub;
              await ICloudStorage.download(
                containerId: iCloudContainerId,
                relativePath: iCloudFilePath,
                destinationFilePath: localFilePath,
                onProgress: (stream) {
                  downloadProgressSub = stream.listen(
                    (progress) =>
                        debugPrint('Download File Progress: $progress'),
                    onDone: () {
                      debugPrint('Download File Done');
                      downloadProgressSub?.cancel();
                      resolve.call(localFilePath);
                    },
                    onError: (exception) {
                      debugPrint('Download File Error: $exception');
                      downloadProgressSub?.cancel();
                      reject.call(exception);
                    },
                    cancelOnError: true,
                  );
                },
              );
            }
          }
          if (!hasFile) {
            resolve.call(null);
            return;
          }
        },
      );
      return Right(filePath);
    } on Exception catch (exception) {
      if (exception is PlatformException &&
          exception.code != PlatformExceptionCode.fileNotFound) {
        return Left(exception);
      } else {
        return const Right(null);
      }
    }
  }

  static Future<Either<Exception, bool>> uploadFileToICloudContainer(
    String iCloudContainerId,
    String localFilePath,
    String iCloudFilePath,
  ) async {
    try {
      final isSuccess = await Promise.run(
        (resolve, reject) async {
          StreamSubscription? uploadProgressSub;
          await ICloudStorage.upload(
            containerId: iCloudContainerId,
            filePath: localFilePath,
            destinationRelativePath: iCloudFilePath,
            onProgress: (stream) {
              uploadProgressSub = stream.listen(
                (progress) => debugPrint('Upload File Progress: $progress'),
                onDone: () {
                  debugPrint('Upload File Done');
                  uploadProgressSub?.cancel();
                  resolve.call(true);
                },
                onError: (exception) {
                  debugPrint('Upload File Error: $exception');
                  uploadProgressSub?.cancel();
                  reject.call(exception);
                },
                cancelOnError: true,
              );
            },
          );
        },
      );
      return Right(isSuccess);
    } on Exception catch (exception) {
      return Left(exception);
    }
  }

  static Future<void> deleteFileOnICloudContainer(
      String iCloudContainerId, String iCloudFilePath) async {
    try {
      debugPrint('deleteFileOnICloudContainer deleting $iCloudFilePath');
      await ICloudStorage.delete(
        containerId: iCloudContainerId,
        relativePath: iCloudFilePath,
      );
      debugPrint('deleteFileOnICloudContainer success');
    } catch (e) {
      debugPrint('deleteFileOnICloudContainer $e');
    }
  }

  static Future<bool> saveFileToDirectory(
      Uint8List data, String filePath) async {
    try {
      final file = File(filePath);
      if (!file.existsSync()) {
        file.createSync(recursive: true);
      }

      await file.writeAsBytes(data, flush: true);

      return true;
    } on Exception catch (error) {
      debugPrint('saveFileToDirectory $error');
    }

    return false;
  }

  static FileExtensionType getFileExtensionTypeByUrl(String? url) {
    try {
      if (url == null || url == '') return FileExtensionType.NONE;

      final urlExtensionUrl = extension(url);
      final urlExtensionComponents = urlExtensionUrl.split('?');
      var extensionName = '';
      if (ListUtility.isNotEmpty(urlExtensionComponents)) {
        extensionName = urlExtensionComponents[0].replaceAll('.', '');
      }
      if (StringUtility.equalsIgnoreCase(
          extensionName, EnumToString.convertToString(FileExtensionType.JPG))) {
        return FileExtensionType.JPG;
      } else if (StringUtility.equalsIgnoreCase(
          extensionName, EnumToString.convertToString(FileExtensionType.PNG))) {
        return FileExtensionType.PNG;
      } else if (StringUtility.equalsIgnoreCase(extensionName,
          EnumToString.convertToString(FileExtensionType.JPEG))) {
        return FileExtensionType.JPEG;
      } else if (StringUtility.equalsIgnoreCase(
          extensionName, EnumToString.convertToString(FileExtensionType.PDF))) {
        return FileExtensionType.PDF;
      } else if (StringUtility.equalsIgnoreCase(
          extensionName, EnumToString.convertToString(FileExtensionType.MP4))) {
        return FileExtensionType.MP4;
      } else if (StringUtility.equalsIgnoreCase(
          extensionName, EnumToString.convertToString(FileExtensionType.MOV))) {
        return FileExtensionType.MOV;
      } else if (StringUtility.equalsIgnoreCase(
          extensionName, EnumToString.convertToString(FileExtensionType.M4V))) {
        return FileExtensionType.M4V;
      } else if (StringUtility.equalsIgnoreCase(extensionName, '3GP')) {
        return FileExtensionType.M3GP;
      }

      return FileExtensionType.NONE;
    } catch (e) {
      debugPrint('getFileExtensionTypeByUrl error $e');
      return FileExtensionType.NONE;
    }
  }

  static FileType getFileType(String? url) {
    final fileExtensionType = getFileExtensionTypeByUrl(url);
    if (fileExtensionType == FileExtensionType.NONE) return FileType.NONE;

    if (fileExtensionType == FileExtensionType.JPEG ||
        fileExtensionType == FileExtensionType.PNG ||
        fileExtensionType == FileExtensionType.JPG) {
      return FileType.IMAGE;
    }

    if (fileExtensionType == FileExtensionType.MP4 ||
        fileExtensionType == FileExtensionType.MOV ||
        fileExtensionType == FileExtensionType.M4V ||
        fileExtensionType == FileExtensionType.M3GP) {
      return FileType.VIDEO;
    }

    if (fileExtensionType == FileExtensionType.PDF) {
      return FileType.DOCUMENT;
    }

    return FileType.NONE;
  }

  static int getFileLength(String filePath) {
    try {
      final file = File(filePath);
      return file.lengthSync();
    } catch (e) {
      debugPrint('getFileLength $e');
    }

    return 0;
  }

  static Future<String> getMediaCompressUrl(
    String storageUrl, {
    Function(String url, double percentage)? onProgressUpdate,
  }) async {
    final fileType = FileUtility.getFileType(storageUrl);
    var newStorageUrl = storageUrl;
    if (fileType == FileType.VIDEO) {
      newStorageUrl = await VideoUtility.getVideCompressed(
        newStorageUrl,
        VideoQuality.MediumQuality,
        onProgressUpdate: onProgressUpdate,
      );
    } else {
      newStorageUrl = await ImageUtility.getImageUrlExifRotate(storageUrl);
      newStorageUrl = await ImageUtility.compressImageFile(newStorageUrl);
      onProgressUpdate?.call(storageUrl, 100.0);
    }

    debugPrint(
        // ignore: lines_longer_than_80_chars
        'File compress $storageUrl => $newStorageUrl fileSize: ${FileUtility.getFileLength(newStorageUrl)}');

    return newStorageUrl;
  }

  static Future<String> convertToOriginalUrl({
    required String originalServerUrl,
    required String serverUrl,
    required String storageUrl,
  }) async {
    if (StringUtility.isEmpty(originalServerUrl) ||
        StringUtility.isEmpty(serverUrl)) {
      return serverUrl;
    }

    final serverUri = Uri.parse(serverUrl);
    final queryParameters = serverUri.queryParameters;
    final serverPath = serverUri.path;

    if (StringUtility.isNotEmpty(queryParameters['w']) &&
        StringUtility.isNotEmpty(queryParameters['h'])) {
      return serverUrl;
    }

    Size? imageSize;

    try {
      if (StringUtility.isNotEmpty(storageUrl)) {
        final fileType = FileUtility.getFileType(storageUrl);
        if (fileType == FileType.IMAGE) {
          imageSize = ImageSizeGetter.getSize(FileInput(File(storageUrl)));
        } else if (fileType == FileType.VIDEO) {
          final videoInfo = await FlutterVideoInfo().getVideoInfo(storageUrl);
          imageSize =
              Size(unwrap(videoInfo?.width, 0), unwrap(videoInfo?.height, 0));
        }
      }
    } catch (e) {
      debugPrint('convertToOriginalUrl-getStorageSize $e');
    }

    try {
      if (imageSize == null || imageSize.width == 0 || imageSize.height == 0) {
        final httpInput = await HttpInput.createHttpInput(serverUrl);
        imageSize = await ImageSizeGetter.getSizeAsync(httpInput);
      }
    } catch (e) {
      debugPrint('convertToOriginalUrl-getServerSize $e');
    }

    if (imageSize != null && imageSize.width != 0 && imageSize.height != 0) {
      // ignore: lines_longer_than_80_chars
      return '$originalServerUrl$serverPath?op=noop&w=${imageSize.width}&h=${imageSize.height}';
    }

    return serverUrl;
  }

  static Future<bool> saveImageToGallery(
      String filePath, String fileName) async {
    try {
      // 请求权限
      if (Platform.isAndroid) {
        if (!await _requestStoragePermission()) {
          debugPrint('saveImageToGallery: Storage permission denied');
          return false;
        }
      }

      // 验证文件是否存在
      final file = File(filePath);
      if (!file.existsSync()) {
        debugPrint('saveImageToGallery: File does not exist at $filePath');
        return false;
      }

      // 读取图片文件
      final bytes = await file.readAsBytes();
      if (bytes.isEmpty) {
        debugPrint('saveImageToGallery: File is empty');
        return false;
      }

      // 保存图片 - 使用多种方式尝试保存
      final isSuccess = await _saveImageWithFallback(bytes, fileName);

      if (isSuccess) {
        debugPrint('saveImageToGallery: Successfully');
        return true;
      } else {
        debugPrint('saveImageToGallery: Failed to save image');
        return false;
      }
    } catch (e) {
      debugPrint('saveImageToGallery error: $e');
      return false;
    }
  }

  /// 使用多种方式尝试保存图片，提高兼容性
  static Future<bool> _saveImageWithFallback(
      Uint8List bytes, String fileName) async {
    try {
      // 方法1: 使用默认方式保存
      var result = await ImageGallerySaver.saveImage(
        bytes,
        quality: 100,
        name: fileName,
      );

      if (result != null && result['isSuccess'] == true) {
        return true;
      }

      debugPrint(
          'saveImageToGallery: Default method failed, trying alternative...');

      // 方法2: 不指定文件名，让系统自动生成
      result = await ImageGallerySaver.saveImage(
        bytes,
        quality: 100,
      );

      if (result != null && result['isSuccess'] == true) {
        return true;
      }

      debugPrint('saveImageToGallery: Alternative method also failed');
      return false;
    } catch (e) {
      debugPrint('_saveImageWithFallback error: $e');
      return false;
    }
  }

  /// 请求存储权限，兼容不同Android版本
  static Future<bool> _requestStoragePermission() async {
    try {
      // Android 13+ (API 33+) 需要特定的媒体权限
      if (Platform.isAndroid) {
        // 检查当前Android版本
        final androidInfo = await _getAndroidVersion();

        if (androidInfo >= 33) {
          // Android 13+ 使用新的媒体权限
          return await _requestMediaPermissions();
        } else if (androidInfo >= 30) {
          // Android 11+ 使用管理外部存储权限
          return await _requestManageExternalStoragePermission();
        } else {
          // Android 10 及以下使用传统存储权限
          return await _requestLegacyStoragePermission();
        }
      }

      return true; // iOS 不需要权限
    } catch (e) {
      debugPrint('_requestStoragePermission error: $e');
      return false;
    }
  }

  /// 获取Android版本
  static Future<int> _getAndroidVersion() async {
    try {
      if (Platform.isAndroid) {
        final deviceInfo = DeviceInfoPlugin();
        final androidInfo = await deviceInfo.androidInfo;
        return androidInfo.version.sdkInt;
      }
      return 30; // 非Android设备默认返回30
    } catch (e) {
      debugPrint('_getAndroidVersion error: $e');
      return 30; // 出错时默认假设为Android 11 (API 30)
    }
  }

  /// Android 13+ 媒体权限
  static Future<bool> _requestMediaPermissions() async {
    try {
      // 检查是否已有权限
      final photosStatus = await Permission.photos.status;
      if (photosStatus.isGranted) {
        return true;
      }

      // 请求权限
      final result = await Permission.photos.request();
      return result.isGranted;
    } catch (e) {
      debugPrint('_requestMediaPermissions error: $e');
      // 如果新权限不可用，回退到传统权限
      return await _requestLegacyStoragePermission();
    }
  }

  /// Android 11+ 管理外部存储权限
  static Future<bool> _requestManageExternalStoragePermission() async {
    try {
      final status = await Permission.manageExternalStorage.status;
      if (status.isGranted) {
        return true;
      }

      final result = await Permission.manageExternalStorage.request();
      if (result.isGranted) {
        return true;
      }

      // 如果管理外部存储权限被拒绝，尝试基本存储权限
      return await _requestLegacyStoragePermission();
    } catch (e) {
      debugPrint('_requestManageExternalStoragePermission error: $e');
      return await _requestLegacyStoragePermission();
    }
  }

  /// 传统存储权限 (Android 10 及以下)
  static Future<bool> _requestLegacyStoragePermission() async {
    try {
      if (await Permission.storage.isGranted) {
        return true;
      }

      final status = await Permission.storage.request();
      return status.isGranted;
    } catch (e) {
      debugPrint('_requestLegacyStoragePermission error: $e');
      return false;
    }
  }
}

enum FileType { VIDEO, IMAGE, DOCUMENT, NONE }

enum FileExtensionType {
  NONE,
  PNG,
  JPG,
  JPEG,
  PDF,
  MP4,
  MOV,
  M4V,
  M3GP,
}
