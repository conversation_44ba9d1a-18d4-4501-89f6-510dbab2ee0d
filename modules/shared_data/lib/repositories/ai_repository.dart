import 'dart:async';

import 'package:dartz/dartz.dart';
import 'package:shared_data/shared_data.dart';
import 'package:shared_service/shared_service.dart';

abstract class AIRepository {
  Future<AIGPTMessageResponse?> getGPTMessage(String message);

  Future<Stream<String>> streamGPTAssistant({
    required String query,
    required Function(AIGPTSuggestionMetadataResponse) onSuggestionMetadata,
    required Function(AIGPTFinancialStatementsResponse) onFinancialStatements,
    required Function(String) onProgress,
    String? threadId,
  });

  Future<bool> cancelGPTAssistant();

  Future<List<AIGPTQuestionResponse>> getGPTQuestions({
    required int page,
    required int limit,
    required String category,
  });

  Future<List<AIGPTQuestionSelectorResponse>> getGPTQuestionSelectors({
    required int page,
    required int limit,
    required String category,
  });

  Future<AIGPTPromptResponse?> getGPTPrompt(String promptCategory);

  Future<AIGPTSecFillingsResponse?> getSecFillings({
    required String ticker,
    required String fillingType,
  });

  Future<List<AIGPTEconomicCalendarResponse>> getEconomicCalendars({
    required int days,
    required String country,
    required String timeFilter,
  });

  Future<List<AIGPTEarningCalendarResponse>> getEarningCalendars({
    required int page,
    required int limit,
    required int days,
    required String timeFilter,
  });

  Future<List<AIGPTSecFillingsTickerResponse>> getSecFillingsTickers({
    required String text,
    required int page,
    required int limit,
  });

  Future<List<AIGPTConversationResponse>?> getConversationHistory({
    required int page,
    required int pageSize,
    String? keywords,
  });

  Future<bool> deleteConversationHistory({
    required int id,
  });

  Future<bool> updateConversationHistory({
    required int id,
  });

  Future<AIGPTFinancialStatementsResponse?> getTickerFacts({
    required String ticker,
    String? queryId,
  });

  Future<void> setFinancialModalTipsCache(String tipsModal);

  Future<String?> readFinancialModalTipsCache();

  Future<void> setHomeAIModalStateCache(String homeAIModal);

  Future<String?> readHomeAIModalStateCache();
}

class AIRepositoryImp extends AIRepository {
  AIRepositoryImp({
    required this.aiService,
    required this.cacheService,
    required this.authRepository,
  });

  final AIService aiService;
  final CacheService cacheService;
  final AuthRepository authRepository;
  final Map<String, AIGPTWebsiteResponse?> websiteCache = {};

  @override
  Future<AIGPTMessageResponse?> getGPTMessage(String message) async {
    final credentials = await authRepository.getCurrentUserCredential();

    Either<Exception, AIGPTMessageResponse?> result;
    if (credentials != null) {
      result = await aiService.getGPTMessage(
        accessToken: credentials.accessToken,
        query: message,
      );
    } else {
      result = await aiService.getPublicGPTMessage(message);
    }

    return result.getOrElse(() => null);
  }

  @override
  Future<Stream<String>> streamGPTAssistant({
    required String query,
    required Function(AIGPTSuggestionMetadataResponse) onSuggestionMetadata,
    required Function(AIGPTFinancialStatementsResponse) onFinancialStatements,
    required Function(String) onProgress,
    String? threadId,
  }) async {
    final credentials = await authRepository.getCurrentUserCredential();

    return aiService.streamGPTAssistant(
      query: query,
      onSuggestionMetadata: onSuggestionMetadata,
      onFinancialStatements: onFinancialStatements,
      onProgress: onProgress,
      accessToken: credentials?.accessToken,
      threadId: threadId,
    );
  }

  @override
  Future<bool> cancelGPTAssistant() async {
    final credentials = await authRepository.getCurrentUserCredential();
    if (credentials == null) return false;

    final isSuccess = aiService.cancelGPTAssistant(credentials.accessToken);

    return isSuccess;
  }

  @override
  Future<List<AIGPTQuestionResponse>> getGPTQuestions({
    required int page,
    required int limit,
    required String category,
  }) async {
    final result = await aiService.getGPTQuestions(
      page: page,
      limit: limit,
      category: category,
    );

    return unwrap(result.getOrElse(() => null), []);
  }

  @override
  Future<List<AIGPTQuestionSelectorResponse>> getGPTQuestionSelectors({
    required int page,
    required int limit,
    required String category,
  }) async {
    final result = await aiService.getGPTQuestionSelectors(
      page: page,
      limit: limit,
      category: category,
    );

    return unwrap(result.getOrElse(() => null), []);
  }

  @override
  Future<AIGPTPromptResponse?> getGPTPrompt(String promptCategory) async {
    final result = await aiService.getGPTPrompts(
      page: 1,
      limit: 10,
      promptCategory: promptCategory,
    );
    final prompts =
        unwrap(result.getOrElse(() => null), <AIGPTPromptResponse>[]);
    if (prompts.isEmpty) return null;

    return prompts.first;
  }

  @override
  Future<AIGPTSecFillingsResponse?> getSecFillings({
    required String ticker,
    required String fillingType,
  }) async {
    final result = await aiService.getSecFillings(
      ticker: ticker,
      fillingType: fillingType,
      page: 1,
      limit: 500,
    );

    return result.getOrElse(() => null);
  }

  @override
  Future<List<AIGPTEconomicCalendarResponse>> getEconomicCalendars({
    required int days,
    required String country,
    required String timeFilter,
  }) async {
    final result = await aiService.getEconomicCalendars(
      page: 1,
      limit: 100,
      days: days,
      country: country,
      timeFilter: timeFilter,
    );

    return unwrap(result.getOrElse(() => null), []);
  }

  @override
  Future<List<AIGPTSecFillingsTickerResponse>> getSecFillingsTickers({
    required String text,
    required int page,
    required int limit,
  }) async {
    final result = await aiService.getSecFillingsTickers(
      text: text,
      page: page,
      limit: limit,
    );

    return unwrap(result.getOrElse(() => []), []);
  }

  @override
  Future<List<AIGPTConversationResponse>?> getConversationHistory({
    required int page,
    required int pageSize,
    String? keywords,
  }) async {
    final credentials = await authRepository.getCurrentUserCredential();
    if (credentials == null) return [];
    final result = await aiService.getConversationHistory(
      accessToken: credentials.accessToken,
      page: page,
      pageSize: pageSize,
      keywords: keywords,
    );

    return unwrap(result.getOrElse(() => []), []);
  }

  @override
  Future<bool> deleteConversationHistory({
    required int id,
  }) async {
    final credentials = await authRepository.getCurrentUserCredential();
    if (credentials == null) return false;
    final result = await aiService.deleteConversationHistory(
        accessToken: credentials.accessToken, id: id);
    return result.getOrElse(() => false);
  }

  @override
  Future<bool> updateConversationHistory({
    required int id,
  }) async {
    final credentials = await authRepository.getCurrentUserCredential();
    if (credentials == null) return false;
    final result = await aiService.updateConversationHistory(
        accessToken: credentials.accessToken, id: id);
    return result.getOrElse(() => false);
  }

  @override
  Future<AIGPTFinancialStatementsResponse?> getTickerFacts({
    required String ticker,
    String? queryId,
  }) async {
    final credentials = await authRepository.getCurrentUserCredential();
    if (credentials == null) return null;
    final result = await aiService.getTickerFacts(
      accessToken: credentials.accessToken,
      ticker: ticker,
      queryId: queryId,
    );
    return result.getOrElse(() => null);
  }

  @override
  Future<String?> readFinancialModalTipsCache() {
    return cacheService.readFinancialModalTipsCache();
  }

  @override
  Future<void> setFinancialModalTipsCache(String tipsModal) {
    return cacheService.setFinancialModalTipsCache(tipsModal);
  }

  @override
  Future<String?> readHomeAIModalStateCache() {
    return cacheService.readHomeAIModalStateCache();
  }

  @override
  Future<void> setHomeAIModalStateCache(String homeAIModal) {
    return cacheService.setHomeAIModalStateCache(homeAIModal);
  }

  @override
  Future<List<AIGPTEarningCalendarResponse>> getEarningCalendars({
    required int page,
    required int limit,
    required int days,
    required String timeFilter,
  }) async {
    final result = await aiService.getEarningCalendars(
      page: page,
      limit: limit,
      days: days,
      timeFilter: timeFilter,
    );

    return unwrap(result.getOrElse(() => null), []);
  }
}
