import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:shared_service/shared_service.dart';

@JsonSerializable()
class AIGPTFinancialStatementsResponse extends Equatable {
  AIGPTFinancialStatementsResponse({
    this.ticker,
    this.companyInfo,
    this.data,
    this.queryId,
  });

  factory AIGPTFinancialStatementsResponse.fromJson(dynamic json) =>
      _$AIGPTCategoryResponseFromJson(json);

  final String? ticker;
  final AIGPTFinancialStatementsCompanyResponse? companyInfo;
  final dynamic data;
  final String? queryId;

  Map<String, Map<String, AIGPTFinancialStatementsDataResponse>>?
      get annualIncomeData {
    return annualData?['Income'];
  }

  Map<String, Map<String, AIGPTFinancialStatementsDataResponse>>?
      get annualBalanceData {
    return annualData?['Balance'];
  }

  Map<String, Map<String, AIGPTFinancialStatementsDataResponse>>?
      get annualCashFlowData {
    return annualData?['CashFlow'];
  }

  Map<String, Map<String, Map<String, AIGPTFinancialStatementsDataResponse>>>?
      get annualData {
    return getDataByType('Annual');
  }

  Map<String, Map<String, AIGPTFinancialStatementsDataResponse>>?
      get quarterlyIncomeData {
    return quarterlyData?['Income'];
  }

  Map<String, Map<String, AIGPTFinancialStatementsDataResponse>>?
      get quarterlyBalanceData {
    return quarterlyData?['Balance'];
  }

  Map<String, Map<String, AIGPTFinancialStatementsDataResponse>>?
      get quarterlyCashFlowData {
    return quarterlyData?['CashFlow'];
  }

  Map<String, Map<String, Map<String, AIGPTFinancialStatementsDataResponse>>>?
      get quarterlyData {
    return getDataByType('Quarterly');
  }

  Map<String, String>? get incomeAvgGrowth3Y {
    return getAvgGrowth('AvgGrowth3Y', 'Income');
  }

  Map<String, String>? get balanceAvgGrowth3Y {
    return getAvgGrowth('AvgGrowth3Y', 'Balance');
  }

  Map<String, String>? get cashFlowAvgGrowth3Y {
    return getAvgGrowth('AvgGrowth3Y', 'CashFlow');
  }

  Map<String, String>? get incomeAvgGrowth5Y {
    return getAvgGrowth('AvgGrowth5Y', 'Income');
  }

  Map<String, String>? get balanceAvgGrowth5Y {
    return getAvgGrowth('AvgGrowth5Y', 'Balance');
  }

  Map<String, String>? get cashFlowAvgGrowth5Y {
    return getAvgGrowth('AvgGrowth5Y', 'CashFlow');
  }

  Map<String, String>? get incomeAvgGrowth3Q {
    return getAvgGrowth('AvgGrowth3Q', 'Income');
  }

  Map<String, String>? get balanceAvgGrowth3Q {
    return getAvgGrowth('AvgGrowth3Q', 'Balance');
  }

  Map<String, String>? get cashFlowAvgGrowth3Q {
    return getAvgGrowth('AvgGrowth3Q', 'CashFlow');
  }

  Map<String, String>? get incomeAvgGrowth5Q {
    return getAvgGrowth('AvgGrowth5Q', 'Income');
  }

  Map<String, String>? get balanceAvgGrowth5Q {
    return getAvgGrowth('AvgGrowth5Q', 'Balance');
  }

  Map<String, String>? get cashFlowAvgGrowth5Q {
    return getAvgGrowth('AvgGrowth5Q', 'CashFlow');
  }

  Map<String, String>? getAvgGrowth(String key, String growthKey) {
    if (data == null) return null;

    if (data is! Map<String, dynamic>) return null;

    if (data[key] == null) return null;

    if (data[key] is! Map<String, dynamic>) return null;

    if (data[key][growthKey] == null) return null;

    if (data[key][growthKey] is! Map<String, dynamic>) return null;

    final jsonData = data[key][growthKey] as Map<String, dynamic>;

    return jsonData.map((key, value) => MapEntry(key, value ?? ''));
  }

  Map<String, Map<String, Map<String, AIGPTFinancialStatementsDataResponse>>>?
      getDataByType(String key) {
    if (data == null) return null;

    if (data is! Map<String, dynamic>) return null;

    if (data[key] == null) return null;

    if (data[key] is! Map<String, dynamic>) return null;

    final annualJsonData = data[key] as Map<String, dynamic>;

    final result = <String,
        Map<String, Map<String, AIGPTFinancialStatementsDataResponse>>>{};

    for (final entry in annualJsonData.entries) {
      final key = entry.key;
      final value = entry.value;

      if (value is! Map<String, dynamic>) continue;

      final innerMap =
          <String, Map<String, AIGPTFinancialStatementsDataResponse>>{};

      for (final innerEntry in value.entries) {
        final innerKey = innerEntry.key;
        final innerValue = innerEntry.value;

        if (innerValue is! Map<String, dynamic>) continue;

        final dataList = <String, AIGPTFinancialStatementsDataResponse>{};

        for (final dataEntry in innerValue.entries) {
          final dataKey = dataEntry.key;
          final dataValue = dataEntry.value;

          if (dataValue is! Map<String, dynamic>) continue;

          dataList[dataKey] =
              AIGPTFinancialStatementsDataResponse.fromJson(dataValue);
        }

        innerMap[innerKey] = dataList;
      }

      result[key] = innerMap;
    }

    return result;
  }

  Map<String, dynamic> toJson() => _$AIGPTCategoryResponseToJson(this);

  @override
  List<Object?> get props => [
        ticker,
        companyInfo,
        data,
        queryId,
      ];
}

AIGPTFinancialStatementsResponse _$AIGPTCategoryResponseFromJson(dynamic json) {
  return AIGPTFinancialStatementsResponse(
    ticker: json['ticker'],
    companyInfo: json['company_info'] == null
        ? null
        : AIGPTFinancialStatementsCompanyResponse.fromJson(
            json['company_info']),
    data: json['data'],
    queryId: json['queryId'],
  );
}

Map<String, dynamic> _$AIGPTCategoryResponseToJson(
        AIGPTFinancialStatementsResponse instance) =>
    <String, dynamic>{
      'ticker': instance.ticker,
      'company_info': instance.companyInfo?.toJson(),
      'data': instance.data,
      'queryId': instance.queryId,
    };
