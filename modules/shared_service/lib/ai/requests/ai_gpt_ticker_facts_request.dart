import 'dart:convert';

import 'package:shared_service/shared_service.dart';

class AIGPTTickerFactsRequest extends Requestable {
  AIGPTTickerFactsRequest({
    required this.accessToken,
    required this.ticker,
    this.queryId,
  });

  final String accessToken;
  String ticker;
  String? queryId;

  @override
  BaseURLType get baseUrlType => BaseURLType.CORE_GPT;

  @override
  get body => null;

  @override
  Encoding? get encoding => null;

  @override
  Map<String, String> get headers => {
        'authorization': 'Bearer $accessToken',
        'Content-Type': 'application/json',
      };

  @override
  HTTPMethod get method => HTTPMethod.GET;

  @override
  String get path {
    var url = AIApiUrl.GET_AI_TICKER_FACTS.replaceAll(':ticker', ticker);
    if (queryId != null) {
      url = '$url?query_id=$queryId';
    }
    return url;
  }

  @override
  Map<String, String?>? get queryParameters => null;
}
