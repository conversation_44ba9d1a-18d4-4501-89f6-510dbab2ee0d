import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';

import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:firebase_performance/firebase_performance.dart'
    as firebase_performance;
import 'package:flutter/widgets.dart';
import 'package:http/http.dart' as http;
import 'package:shared_data/shared_data.dart';
import 'package:shared_service/shared_service.dart';

abstract class AIService {
  Future<Either<Exception, AIGPTMessageResponse?>> getGPTMessage({
    required String accessToken,
    required String query,
  });

  Future<Either<Exception, AIGPTMessageResponse?>> getPublicGPTMessage(
      String query);

  Stream<String> streamGPTAssistant({
    required String query,
    required Function(AIGPTSuggestionMetadataResponse) onSuggestionMetadata,
    required Function(AIGPTFinancialStatementsResponse) onFinancialStatements,
    required Function(String) onProgress,
    String? accessToken,
    String? threadId,
  });

  Future<bool> cancelGPTAssistant(String accessToken);

  Future<Either<Exception, List<AIGPTQuestionResponse>?>> getGPTQuestions({
    required int page,
    required int limit,
    required String category,
  });

  Future<Either<Exception, List<AIGPTQuestionSelectorResponse>?>>
      getGPTQuestionSelectors({
    required int page,
    required int limit,
    required String category,
  });

  Future<Either<Exception, List<AIGPTPromptResponse>?>> getGPTPrompts({
    required int page,
    required int limit,
    required String promptCategory,
  });

  Future<Either<Exception, AIGPTSecFillingsResponse?>> getSecFillings({
    required int page,
    required int limit,
    required String ticker,
    required String fillingType,
  });

  Future<Either<Exception, List<AIGPTEconomicCalendarResponse>?>>
      getEconomicCalendars({
    required int page,
    required int limit,
    required int days,
    required String country,
    required String timeFilter,
  });

  Future<Either<Exception, List<AIGPTEarningCalendarResponse>?>>
      getEarningCalendars({
    required int page,
    required int limit,
    required int days,
    required String timeFilter,
  });

  Future<Either<Exception, List<AIGPTSecFillingsTickerResponse>?>>
      getSecFillingsTickers({
    required String text,
    required int page,
    required int limit,
  });

  Future<Either<Exception, List<AIGPTConversationResponse>?>>
      getConversationHistory({
    required String accessToken,
    required int page,
    required int pageSize,
    String? keywords,
  });

  Future<Either<Exception, bool>> deleteConversationHistory({
    required String accessToken,
    required int id,
  });

  Future<Either<Exception, bool>> updateConversationHistory({
    required String accessToken,
    required int id,
  });

  Future<Either<Exception, AIGPTFinancialStatementsResponse?>> getTickerFacts({
    required String accessToken,
    required String ticker,
    String? queryId,
  });
}

class AIServiceImpl extends BaseService implements AIService {
  AIServiceImpl({
    required HttpClient client,
    required this.environment,
    required this.firebasePerformance,
    required this.dio,
    required this.toolDebugManager,
  }) : super(client) {
    toolDebugManager.setupDio(dio);
  }

  final Environment environment;
  final firebase_performance.FirebasePerformance firebasePerformance;
  final Dio dio;
  final ToolDebugManager toolDebugManager;
  CancelToken? currentAssistantCancelToken;

  @override
  Future<Either<Exception, AIGPTMessageResponse?>> getGPTMessage({
    required String accessToken,
    required String query,
  }) async {
    final request = AIGPTAnswerRequest(
      accessToken: accessToken,
      query: query,
    );

    final result = await client.send(request);
    return result.fold(
      Left.new,
      (response) {
        return ResponseMapper.map<AIGPTMessageResponse?>(
          response,
          (dynamic json) {
            return AIGPTMessageResponse.fromJson(json);
          },
        );
      },
    );
  }

  @override
  Future<Either<Exception, AIGPTMessageResponse?>> getPublicGPTMessage(
      String query) async {
    final request = AIPublicGPTAnswerRequest(
      query: query,
    );

    final result = await client.send(request);
    return result.fold(
      Left.new,
      (response) {
        return ResponseMapper.map<AIGPTMessageResponse?>(
          response,
          (dynamic json) {
            return AIGPTMessageResponse.fromJson(json);
          },
        );
      },
    );
  }

  @override
  Stream<String> streamGPTAssistant({
    required String query,
    required Function(AIGPTSuggestionMetadataResponse) onSuggestionMetadata,
    required Function(AIGPTFinancialStatementsResponse) onFinancialStatements,
    required Function(String) onProgress,
    String? accessToken,
    String? threadId,
  }) async* {
    final baseUrl = environment.baseUrlSets[BaseURLType.CORE_GPT];
    var url = Uri.parse('$baseUrl${AIApiUrl.POST_PUBLIC_GPT_ASSISTANT_QUERY}');
    if (StringUtility.isNotEmpty(accessToken)) {
      // await cancelGPTAssistant(unwrap(accessToken, ''));
      url = Uri.parse('$baseUrl${AIApiUrl.POST_GPT_ASSISTANT_QUERY}');
    }

    final payload = {
      'query': query,
      if (threadId != null) 'thread_id': threadId,
      'model': 'openai/gpt-4-turbo',
      'stream': true,
      'cost_sensitive': false,
      'new_round': false,
      'include_summary': true,
      'max_tokens': 200,
      'auto_functions': true,
      'provider_model': 'openai/gpt-4-turbo',
    };

    final request = http.Request('POST', url);
    request.headers['Content-Type'] = 'application/json';
    request.headers['Accept'] = 'text/event-stream';
    if (StringUtility.isNotEmpty(accessToken)) {
      request.headers['Authorization'] = 'Bearer $accessToken';
    }
    request.body = jsonEncode(payload);

    final metric = firebasePerformance.newHttpMetric(
        url.toString(), firebase_performance.HttpMethod.Post);

    /// start tracking
    await metric.start();

    currentAssistantCancelToken = CancelToken();
    final response = await dio.post(
      url.toString(),
      options: Options(
        responseType: ResponseType.stream,
        headers: {
          'Authorization': 'Bearer $accessToken',
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream',
        },
        validateStatus: (status) {
          return status != null && status <= 500;
        },
        receiveTimeout: const Duration(seconds: 300),
        sendTimeout: const Duration(seconds: 300),
      ),
      data: jsonEncode(payload),
      cancelToken: currentAssistantCancelToken,
    );

    if (response.statusCode != 200) {
      metric
        ..responsePayloadSize =
            int.parse(response.headers.value('Content-Length') ?? '0')
        ..responseContentType = response.headers.value('Content-Type')
        ..httpResponseCode = response.statusCode;
      await metric.stop();

      if (response.statusCode == 429) {
        Stream<String> errorStream = response.data.stream.transform(
          StreamTransformer.fromHandlers(
            handleData: (Uint8List data, EventSink<String> sink) {
              sink.add(utf8.decode(data.toList()));
            },
          ),
        );
        var errorMsg = '';
        try {
          await for (String data in errorStream) {
            errorMsg = jsonDecode(data)['detail'];
          }
          // ignore: empty_catches
        } catch (e) {}
        throw Exception('429:$errorMsg');
      }

      throw Exception('Failed to connect: ${response.statusCode}');
    }
    // Access the byte stream from the response
    final byteStream = response.data.stream;

    // Use StreamTransformer.fromHandlers
    // to decode the byte stream into a string
    Stream<String> stringStream = byteStream.transform(
      StreamTransformer.fromHandlers(
        handleData: (Uint8List data, EventSink<String> sink) {
          // Decode the byte data into a string and add it to the sink
          sink.add(utf8.decode(data.toList()));
        },
      ),
    );

    // Process the stream
    final stream = stringStream
        .transform(const LineSplitter())
        .where((line) => line.startsWith('data: '))
        .map((line) => line.substring(6)) // Remove 'data: ' prefix
        .map((jsonStr) {
          // debugPrint('jsonStr content: $jsonStr');
          final json = jsonDecode(jsonStr) as Map<String, dynamic>;
          if (StringUtility.equalsIgnoreCase(json['type'], 'progress')) {
            onProgress(json['text']);
          }
          if (json['reference_urls'] != null ||
              json['follow_up_questions'] != null) {
            final metadata = AIGPTSuggestionMetadataResponse.fromJson(json);
            onSuggestionMetadata(metadata);
          }
          // debugPrint('stream type ${json['type']}');
          if (json['type'] == 'company_facts' &&
              json['data'] != null &&
              MapUtility.isNotEmpty(json['data']['data'])) {
            // debugPrint('stream type company_facts ${json['data']}');
            final data =
                AIGPTFinancialStatementsResponse.fromJson(json['data']);
            onFinancialStatements(data);
          }
          if (StringUtility.isNotEmpty(json['error']) ||
              json['type'].toString().toLowerCase() == 'error') {
            throw Exception('Error');
          }
          return json;
        })
        .where((data) =>
            data.containsKey('content') && data['content'] != '[DONE]')
        .map((data) => data['content'] as String);

    // Store thread ID if it's returned
    // var threadIdCaptured = threadId != null;

    await for (final data in stream) {
      // If we receive a thread_id and haven't captured it yet, store it
      // if (!threadIdCaptured && data is Map && data.contains('thread_id')) {
      //   threadId = data['thread_id'];
      //   threadIdCaptured = true;
      // }

      if (data.isNotEmpty) {
        yield data;
      }
    }

    metric
      ..responsePayloadSize =
          int.parse(response.headers.value('Content-Length') ?? '0')
      ..responseContentType = response.headers.value('Content-Type')
      ..httpResponseCode = response.statusCode;
    await metric.stop();
  }

  @override
  Future<bool> cancelGPTAssistant(String accessToken) async {
    if (currentAssistantCancelToken != null) {
      currentAssistantCancelToken?.cancel('cancelled');
    }

    final request = AIGPTCancelAnswerRequest(
      accessToken: accessToken,
    );

    final result = await client.send(request);
    return result.fold(
      (l) {
        debugPrint('Error cancelling assistant: $l');
        return false;
      },
      (response) {
        return true;
      },
    );
  }

  @override
  Future<Either<Exception, List<AIGPTQuestionResponse>?>> getGPTQuestions({
    required int page,
    required int limit,
    required String category,
  }) async {
    final request = AIGPTQuestionsRequest(
      page: page,
      limit: limit,
      category: category,
    );

    final result = await client.send(request);
    return result.fold(
      Left.new,
      (response) {
        return ResponseMapper.map<List<AIGPTQuestionResponse>?>(
          response,
          (dynamic json) {
            return ListUtility.parseJsonElementToList(
                json, 'list', AIGPTQuestionResponse.fromJson);
          },
        );
      },
    );
  }

  @override
  Future<Either<Exception, List<AIGPTQuestionSelectorResponse>?>>
      getGPTQuestionSelectors({
    required int page,
    required int limit,
    required String category,
  }) async {
    final request = AIGPTQuestionSelectorsRequest(
      page: page,
      limit: limit,
      category: category,
    );

    final result = await client.send(request);
    return result.fold(
      Left.new,
      (response) {
        return ResponseMapper.map<List<AIGPTQuestionSelectorResponse>?>(
          response,
          (dynamic json) {
            return ListUtility.parseJsonElementToList(
                json, 'list', AIGPTQuestionSelectorResponse.fromJson);
          },
        );
      },
    );
  }

  @override
  Future<Either<Exception, List<AIGPTPromptResponse>?>> getGPTPrompts({
    required int page,
    required int limit,
    required String promptCategory,
  }) async {
    final request = AIGPTPromptSelectorsRequest(
      page: page,
      limit: limit,
      promptCategory: promptCategory,
    );

    final result = await client.send(request);
    return result.fold(
      Left.new,
      (response) {
        return ResponseMapper.map<List<AIGPTPromptResponse>?>(
          response,
          (dynamic json) {
            return ListUtility.parseJsonElementToList(
                json, 'list', AIGPTPromptResponse.fromJson);
          },
        );
      },
    );
  }

  @override
  Future<Either<Exception, AIGPTSecFillingsResponse?>> getSecFillings({
    required int page,
    required int limit,
    required String ticker,
    required String fillingType,
  }) async {
    final request = AIGPTSecFillingsRequest(
      page: page,
      limit: limit,
      ticker: ticker,
      fillingType: fillingType,
    );

    final result = await client.send(request);
    return result.fold(
      Left.new,
      (response) {
        return ResponseMapper.map<AIGPTSecFillingsResponse?>(
          response,
          (dynamic json) {
            return AIGPTSecFillingsResponse.fromJson(json);
          },
        );
      },
    );
  }

  @override
  Future<Either<Exception, List<AIGPTEconomicCalendarResponse>?>>
      getEconomicCalendars({
    required int page,
    required int limit,
    required int days,
    required String country,
    required String timeFilter,
  }) async {
    final request = AIGPTEconomicCalendarRequest(
      page: page,
      limit: limit,
      days: days,
      country: country,
      timeFilter: timeFilter,
    );

    final result = await client.send(request);
    return result.fold(
      Left.new,
      (response) {
        return ResponseMapper.map<List<AIGPTEconomicCalendarResponse>?>(
          response,
          (dynamic json) {
            return ListUtility.parseJsonElementToList(
                json, 'events', AIGPTEconomicCalendarResponse.fromJson);
          },
        );
      },
    );
  }

  @override
  Future<Either<Exception, List<AIGPTSecFillingsTickerResponse>?>>
      getSecFillingsTickers({
    required String text,
    required int page,
    required int limit,
  }) async {
    final request = AIGPTSecFillingsTickerRequest(
      text: text,
      page: page,
      limit: limit,
    );

    final result = await client.send(request);
    return result.fold(
      Left.new,
      (response) {
        return ResponseMapper.map<List<AIGPTSecFillingsTickerResponse>?>(
          response,
          (dynamic json) {
            return ListUtility.parseJsonElementToList(
                json, 'tickers', AIGPTSecFillingsTickerResponse.fromJson);
          },
        );
      },
    );
  }

  @override
  Future<Either<Exception, List<AIGPTConversationResponse>?>>
      getConversationHistory({
    required String accessToken,
    required int page,
    required int pageSize,
    String? keywords,
  }) async {
    final request = AIGPTCoversationHistoryRequest(
      page: page,
      limit: pageSize,
      accessToken: accessToken,
      keywords: keywords,
    );

    final result = await client.send(request);
    return result.fold(
      Left.new,
      (response) {
        return ResponseMapper.map<List<AIGPTConversationResponse>?>(
          response,
          (dynamic json) {
            return ListUtility.parseJsonElementToList(
              json,
              'queries',
              AIGPTConversationResponse.fromJson,
            );
          },
        );
      },
    );
  }

  @override
  Future<Either<Exception, bool>> deleteConversationHistory({
    required String accessToken,
    required int id,
  }) async {
    final request = AIGPTCoversationDeleteRequest(
      id: id,
      accessToken: accessToken,
    );

    final result = await client.send(request);
    return result.fold(
      Left.new,
      (response) {
        return const Right(true);
      },
    );
  }

  @override
  Future<Either<Exception, bool>> updateConversationHistory({
    required String accessToken,
    required int id,
  }) async {
    final request = AIGPTCoversationUpdateRequest(
      id: id,
      accessToken: accessToken,
    );

    final result = await client.send(request);
    return result.fold(
      Left.new,
      (response) {
        return const Right(true);
      },
    );
  }

  @override
  Future<Either<Exception, AIGPTFinancialStatementsResponse?>> getTickerFacts({
    required String accessToken,
    required String ticker,
    String? queryId,
  }) async {
    final request = AIGPTTickerFactsRequest(
      ticker: ticker,
      accessToken: accessToken,
      queryId: queryId,
    );

    final result = await client.send(request);
    return result.fold(
      Left.new,
      (response) {
        return ResponseMapper.map<AIGPTFinancialStatementsResponse?>(
          response,
          (dynamic json) {
            return AIGPTFinancialStatementsResponse.fromJson(
                json['company_fact']);
          },
        );
      },
    );
  }

  @override
  Future<Either<Exception, List<AIGPTEarningCalendarResponse>?>>
      getEarningCalendars({
    required int page,
    required int limit,
    required int days,
    required String timeFilter,
  }) async {
    final request = AIGPTEarningsCalendarRequest(
      page: page,
      limit: limit,
      days: days,
      timeFilter: timeFilter,
    );

    final result = await client.send(request);
    return result.fold(
      Left.new,
      (response) {
        return ResponseMapper.map<List<AIGPTEarningCalendarResponse>?>(
          response,
          (dynamic json) {
            return ListUtility.parseJsonElementToList(
                json, 'events', AIGPTEarningCalendarResponse.fromJson);
          },
        );
      },
    );
  }
}
