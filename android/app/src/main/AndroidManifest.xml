<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.addxgo.wallet">

    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" tools:node="remove"/>
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" tools:node="remove"/>
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission
        android:name="com.google.android.gms.permission.AD_ID"
        tools:node="remove" />
    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
    <uses-permission
        android:name="android.permission.USE_FULL_SCREEN_INTENT"
        tools:node="remove" />
    <queries>
        <!-- Explicit apps you know in advance about: -->
        <package
            android:name="com.instagram.android"/>
        <package
            android:name="com.zhiliaoapp.musically"/>
        <package
            android:name="com.facebook.katana"/>
        <package
            android:name="com.facebook.orca"/>
        <package
            android:name="org.telegram.messenger"/>
        <package
            android:name="com.whatsapp"/>
        <package
            android:name="com.twitter.android"/>
        <provider
            android:authorities="com.facebook.katana.provider.PlatformProvider"/>
        <!-- allows app to access Facebook app features -->
        <provider
            android:authorities="com.facebook.orca.provider.PlatformProvider"/>
        <!-- allows sharing to Messenger app -->
    </queries>
    <application
        android:usesCleartextTraffic="true"
        android:enableOnBackInvokedCallback="false"
        android:requestLegacyExternalStorage="true"
        android:hardwareAccelerated="true"
        android:label="@string/app_name"
        android:name="ADDXGoApplication"
        android:icon="@mipmap/ic_launcher"
        android:allowBackup="false"
        android:fullBackupContent="false"
        android:showWhenLocked="true"
        android:turnScreenOn="true">

        <meta-data
            android:name="io.branch.sdk.BranchKey"
            android:value="@string/branch_key" />
        <meta-data
            android:name="io.branch.sdk.BranchKey.test"
            android:value="@string/branch_test_key" />
        <meta-data
            android:name="io.branch.sdk.TestMode"
            android:value="false" />

        <!-- facebook start -->

<!--        <provider android:name="com.facebook.FacebookContentProvider"-->
<!--            android:authorities="com.facebook.app.FacebookContentProvider396733120108502"-->
<!--            android:exported="true" />-->
        <meta-data android:name="com.facebook.sdk.ApplicationId"
            android:value="@string/facebook_app_id"/>
        <meta-data android:name="com.facebook.sdk.ClientToken"
            android:value="@string/facebook_client_token"/>
        <meta-data android:name="com.facebook.sdk.ApplicationName" android:value="${applicationName}"/>
        <activity android:name="com.facebook.FacebookActivity"
            android:configChanges=
                "keyboard|keyboardHidden|screenLayout|screenSize|orientation"
            android:label="@string/app_name" />

        <activity
            android:name="com.facebook.CustomTabActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="@string/fb_login_protocol_scheme" />
            </intent-filter>
        </activity>

        <!-- facebook end -->

        <activity android:name=".TransakActivity"
            enableOnBackInvokedCallback="false"
            android:theme="@android:style/Theme.NoTitleBar"/>

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@mipmap/notification_logo" />

        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:enableOnBackInvokedCallback="false"
            android:launchMode="singleTask"
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">
            <meta-data android:name="flutter_deeplinking_enabled" android:value="false" />
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
                android:name="io.flutter.embedding.android.NormalTheme"
                android:resource="@style/NormalTheme"/>
            <!-- Displays an Android View that continues showing the launch screen
                     Drawable until Flutter paints its first frame, then this splash
                     screen fades out. A splash screen is useful to avoid any visual
                     gap between the end of Android's launch screen and the painting of
                     Flutter's first frame. -->
            <meta-data
                android:name="io.flutter.embedding.android.SplashScreenDrawable"
                android:resource="@drawable/launch_background"/>

            <!--  share intent - start         -->
            <intent-filter>
                <action android:name="android.intent.action.SEND" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="text/*" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.SEND" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="image/*" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.SEND_MULTIPLE" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="image/*" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.SEND" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="video/*" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.SEND_MULTIPLE" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="video/*" />
            </intent-filter>
            <!--  share intent - end         -->

            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
            <intent-filter>
                <action
                    android:name="android.intent.action.VIEW"/>
                <category
                    android:name="android.intent.category.DEFAULT"/>
                <category
                    android:name="android.intent.category.BROWSABLE"/>
                <data
                    android:scheme="${scheme}"
                    android:host="app.addxgo.io"/>
            </intent-filter>
            <intent-filter>
                <action
                    android:name="android.intent.action.VIEW"/>
                <category
                    android:name="android.intent.category.DEFAULT"/>
                <category
                    android:name="android.intent.category.BROWSABLE"/>
                <data
                    android:scheme="${scheme}"/>
            </intent-filter>
            <intent-filter
                android:autoVerify="true"
                tools:targetApi="m">
                <action
                    android:name="android.intent.action.VIEW"/>
                <category
                    android:name="android.intent.category.DEFAULT"/>
                <category
                    android:name="android.intent.category.BROWSABLE"/>
                <!-- DEV -->
                <data
                    android:scheme="https"
                    android:host="dev.addxgo.io"
                    android:pathPrefix="/*"/>
                <data
                    android:scheme="https"
                    android:host="dev.addxgo.io"
                    android:pathPrefix="/community"/>
                <data
                    android:scheme="https"
                    android:host="go-wallet-dev-396512.firebaseapp.com"
                    android:pathPrefix="/*"/>
                <data
                    android:scheme="https"
                    android:host="dev.addxgo.io"
                    android:pathPrefix="/invite"/>
                <data
                    android:scheme="https"
                    android:host="dev.addxgo.io"
                    android:pathPrefix="/referral"/>
                <data
                    android:scheme="https"
                    android:host="dev.addxgo.io"
                    android:pathPrefix="/resetPassword"/>
                <!-- ===== -->
                <!-- QA -->
                <data
                    android:scheme="https"
                    android:host="test.addxgo.io"
                    android:pathPrefix="/*"/>
                <data
                    android:scheme="https"
                    android:host="test.addxgo.io"
                    android:pathPrefix="/community"/>
                <data
                    android:scheme="https"
                    android:host="go-wallet-test-396515.firebaseapp.com"
                    android:pathPrefix="/*"/>
                <data
                    android:scheme="https"
                    android:host="test.addxgo.io"
                    android:pathPrefix="/invite"/>
                <data
                    android:scheme="https"
                    android:host="test.addxgo.io"
                    android:pathPrefix="/referral"/>
                <data
                    android:scheme="https"
                    android:host="test.addxgo.io"
                    android:pathPrefix="/resetPassword"/>
                <!-- ==== -->
                <!-- Prod -->
                <data
                    android:scheme="https"
                    android:host="addxgo.io"
                    android:pathPrefix="/*"/>
                <data
                    android:scheme="https"
                    android:host="addxgo.io"
                    android:pathPrefix="/community"/>
                <data
                    android:scheme="https"
                    android:host="addxgo.io"
                    android:pathPrefix="/invite"/>
                <data
                    android:scheme="https"
                    android:host="addxgo.io"
                    android:pathPrefix="/referral"/>
                <data
                    android:scheme="https"
                    android:host="addxgo.io"
                    android:pathPrefix="/resetPassword"/>
                <data
                    android:scheme="https"
                    android:host="go-wallet-production-396515.firebaseapp.com"
                    android:pathPrefix="/*"/>
                <!-- ==== -->

                <!-- Adjust dev -->
                <data
                    android:scheme="https"
                    android:host="4bpf.adj.st"
                    android:pathPrefix="/invite"/>
                <data
                    android:scheme="https"
                    android:host="app.adjust.com"
                    android:pathPrefix="/1cm8m0rp"/>
                <!-- ==== -->
                
                <!-- Adjust test -->
                <data
                    android:scheme="https"
                    android:host="s5fn.adj.st"
                    android:pathPrefix="/invite"/>
                <data
                    android:scheme="https"
                    android:host="app.adjust.com"
                    android:pathPrefix="/1car41j2"/>
                <!-- ==== -->

                <!-- Adjust prod -->
                <data
                    android:scheme="https"
                    android:host="24xt.adj.st"
                    android:pathPrefix="/invite"/>
                <data
                    android:scheme="https"
                    android:host="app.adjust.com"
                    android:pathPrefix="/1cyzyarr"/>
                <data
                    android:scheme="https"
                    android:host="64kkf.test-app.link"
                    android:pathPrefix="/*"/>
                <data
                    android:scheme="https"
                    android:host="64kkf-alternate.test-app.link"
                    android:pathPrefix="/*"/>
                <data
                    android:scheme="https"
                    android:host="64kkf.app.link"
                    android:pathPrefix="/*"/>
                <data
                    android:scheme="https"
                    android:host="64kkf-alternate.app.link"
                    android:pathPrefix="/*"/>
                <!-- ==== -->

            </intent-filter>
        </activity>
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2"/>
        <receiver
            android:name="com.adjust.sdk.AdjustReferrerReceiver"
            android:permission="android.permission.INSTALL_PACKAGES"
            android:exported="true">
            <intent-filter>
                <action
                    android:name="com.android.vending.INSTALL_REFERRER"/>
            </intent-filter>
        </receiver>
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.social.share.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths"/>
        </provider>
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths"/>
        </provider>
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths"/>
        </provider>
        <provider
            android:name="com.shekarmudaliyar.social_share.SocialSharePluginFileProvider"
            android:authorities="${applicationId}.com.shekarmudaliyar.social_share"
            android:exported="false"
            android:grantUriPermissions="true"
            tools:replace="android:authorities">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths"/>
        </provider>
        <activity
            android:name=".SettingActivity"
            android:exported="true"
            android:theme="@style/LaunchTheme">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.NOTIFICATION_PREFERENCES" />
            </intent-filter>
        </activity>
    </application>
</manifest>