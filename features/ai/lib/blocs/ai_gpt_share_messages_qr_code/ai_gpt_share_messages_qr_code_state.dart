part of 'ai_gpt_share_messages_qr_code_cubit.dart';

class AIGPTShareMessagesQrCodeState extends Equatable {
  const AIGPTShareMessagesQrCodeState({
    required this.type,
    required this.title,
    this.fileName,
    this.filePath,
    this.exception,
    this.socialShareMessageType = AIGPTSocialShareMessageType.NONE,
  });

  final AIGPTShareMessagesQrCodeStateType type;
  final String title;
  final String? fileName;
  final String? filePath;
  final Exception? exception;
  final AIGPTSocialShareMessageType socialShareMessageType;

  AIGPTShareMessagesQrCodeState copyWith({
    AIGPTShareMessagesQrCodeStateType? type,
    String? title,
    String? fileName,
    String? filePath,
    Exception? exception,
    AIGPTSocialShareMessageType? socialShareMessageType,
  }) {
    return AIGPTShareMessagesQrCodeState(
      type: type ?? this.type,
      title: title ?? this.title,
      fileName: fileName ?? this.fileName,
      filePath: filePath ?? this.filePath,
      exception: exception ?? this.exception,
      socialShareMessageType:
          socialShareMessageType ?? this.socialShareMessageType,
    );
  }

  @override
  List<Object?> get props => [
        type,
        fileName,
        filePath,
        exception,
        title,
        socialShareMessageType,
      ];
}

enum AIGPTShareMessagesQrCodeStateType {
  INIT,
  LOADING,
  LOADED,
  ERROR,
  SAVE_FILE_TO_DOWNLOAD_SUCCESS,
  SAVED
}

enum AIGPTSocialShareMessageType {
  TELEGRAM,
  FACEBOOK,
  X,
  WHATSAPP,
  NONE,
  SYSTEM_SHARE
}
