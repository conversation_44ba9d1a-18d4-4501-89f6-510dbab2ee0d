import 'dart:typed_data';

import 'package:addx_go_wallet/bloc/blocs.dart';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:shared_data/shared_data.dart';
import 'package:translator/translator_service.dart';

part 'ai_gpt_share_messages_qr_code_state.dart';

class AIGPTShareMessagesQrCodeCubit
    extends Cubit<AIGPTShareMessagesQrCodeState> {
  AIGPTShareMessagesQrCodeCubit({
    required this.authCubit,
    required this.authRepository,
    required this.platformHelper,
    required String title,
  }) : super(AIGPTShareMessagesQrCodeState(
          type: AIGPTShareMessagesQrCodeStateType.INIT,
          title: title,
        )) {
    init();
  }

  final AuthCubit authCubit;
  final AuthRepository authRepository;
  final PlatformHelper platformHelper;

  void init() async {
    emit(state.copyWith(type: AIGPTShareMessagesQrCodeStateType.LOADING));

    final authState = authCubit.state;
    final userIdentifier = authState.userIdentifier;
    final fileName = '${state.title}_$userIdentifier.png';

    emit(state.copyWith(
      type: AIGPTShareMessagesQrCodeStateType.LOADED,
      fileName: fileName,
    ));
  }

  void showLoading() {
    emit(state.copyWith(type: AIGPTShareMessagesQrCodeStateType.LOADING));
  }

  void exportFile(
    Uint8List? uint8list, {
    bool isBlockUI = false,
    bool requiredSaveFileToDownload = false,
    AIGPTSocialShareMessageType? socialShareMessageType,
  }) async {
    if (isBlockUI) {
      emit(state.copyWith(type: AIGPTShareMessagesQrCodeStateType.LOADING));
    }

    if (uint8list == null || StringUtility.isEmpty(state.fileName)) {
      emit(state.copyWith(
        type: AIGPTShareMessagesQrCodeStateType.ERROR,
        exception: Exception('Data is error'),
      ));
      return;
    }

    final filePath =
        await FileUtility.getDocumentFilePath(unwrap(state.fileName, ''));

    debugPrint('filePath $filePath');

    if (!FileUtility.isFileExisted(filePath)) {
      final success = await saveFile(uint8list, filePath);
      if (success) {
        debugPrint('Save file to document folder success');
        emit(state.copyWith(
          type: AIGPTShareMessagesQrCodeStateType.SAVED,
          filePath: filePath,
          socialShareMessageType: socialShareMessageType,
        ));
      } else {
        debugPrint('Save file to document folder error');
        emit(state.copyWith(
          type: AIGPTShareMessagesQrCodeStateType.ERROR,
          exception: Exception('Save file error'),
        ));
      }
    } else {
      debugPrint('File existed inside document folder');
      emit(state.copyWith(
        type: AIGPTShareMessagesQrCodeStateType.SAVED,
        filePath: filePath,
        socialShareMessageType: socialShareMessageType,
      ));
    }

    emit(state.copyWith(
        socialShareMessageType: AIGPTSocialShareMessageType.NONE));

    if (requiredSaveFileToDownload && FileUtility.isFileExisted(filePath)) {
      saveFileToDownload(filePath);
    }
  }

  void saveFileToDownload(String filePath) async {
    emit(state.copyWith(type: AIGPTShareMessagesQrCodeStateType.LOADING));

    final isSuccess = await FileUtility.saveImageToGallery(
      filePath,
      unwrap(state.fileName, '${DateTime.now().millisecond}.png'),
    );
    if (isSuccess) {
      emit(state.copyWith(
        type: AIGPTShareMessagesQrCodeStateType.SAVE_FILE_TO_DOWNLOAD_SUCCESS,
      ));
    } else {
      emit(state.copyWith(
        type: AIGPTShareMessagesQrCodeStateType.ERROR,
        exception: Exception(
            TranslatorService.translate('__GPTScreen_SaveError_Label__')),
      ));
    }
  }

  Future<bool> saveFile(Uint8List data, String filePath) {
    return FileUtility.saveFileToDirectory(data, filePath);
  }
}
