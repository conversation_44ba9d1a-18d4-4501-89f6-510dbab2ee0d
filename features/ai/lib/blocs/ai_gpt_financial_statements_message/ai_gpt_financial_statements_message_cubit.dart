import 'package:equatable/equatable.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_data/shared_data.dart';
import 'package:shared_service/ai/ai.dart';
import 'package:shared_ui/shared_ui.dart';

part 'ai_gpt_financial_statements_message_state.dart';

class AIGPTFinancialStatementsMessageCubit
    extends Cubit<AIGPTFinancialStatementsMessageState> {
  AIGPTFinancialStatementsMessageCubit({
    required this.aiRepository,
    required AIGPTMessage aiGPTMessage,
  }) : super(AIGPTFinancialStatementsMessageState(
          type: AIGPTFinancialStatementsMessageStateType.INIT,
          aiGPTMessage: aiGPTMessage,
        )) {
    _init();
  }

  final AIRepository aiRepository;

  void _init() {
    if (ListUtility.isEmpty(state.aiGPTMessage.financialStatements)) {
      return;
    }

    final annualStatementSheetGroup =
        state.aiGPTMessage.annualStatementSheetGroup;
    final annualIncomeStatementColumns =
        annualStatementSheetGroup?.incomeStatement?.columns;
    final quarterlyStatementSheetGroup =
        state.aiGPTMessage.quarterlyStatementSheetGroup;
    StatementSheetExcelCell? statementCellSelected;
    if (ListUtility.isNotEmpty(annualIncomeStatementColumns) &&
        unwrap(annualIncomeStatementColumns?.first.cells, []).length >= 2) {
      statementCellSelected = annualIncomeStatementColumns?.first.cells[1];
    }

    emit(state.copyWith(
      type: AIGPTFinancialStatementsMessageStateType.LOADED,
      annualStatementSheetGroup: annualStatementSheetGroup,
      quarterlyStatementSheetGroup: quarterlyStatementSheetGroup,
      statementCellSelected: statementCellSelected,
    ));

    if (statementCellSelected != null) {
      onCellSelected(statementCellSelected);
    }
  }

  void onTaxIndexChanged(int taxIndex) {
    emit(state.copyWith(
      type: AIGPTFinancialStatementsMessageStateType.LOADED,
      taxIndex: taxIndex,
    ));

    final statementCellSelected = state.statementCellSelected;
    if (statementCellSelected != null) {
      onCellSelected(statementCellSelected);
    }
  }

  void onFinancialIndexChanged(int financialIndex) async {
    if (state.aiGPTMessage.financialStatements?[financialIndex].companyInfo ==
        null) {
      emit(state.copyWith(
        type: AIGPTFinancialStatementsMessageStateType.LOADING,
        financialIndex: financialIndex,
      ));
      final ticker = unwrap(
          state.aiGPTMessage.financialStatements![financialIndex].ticker, '');
      final queryId =
          state.aiGPTMessage.financialStatements![financialIndex].queryId;
      final result = await aiRepository.getTickerFacts(
        ticker: ticker,
        queryId: queryId,
      );
      if (result != null && result.companyInfo != null) {
        var oldData = [...state.aiGPTMessage.financialStatements!];
        oldData[financialIndex] = result;

        emit(state.copyWith(
          type: AIGPTFinancialStatementsMessageStateType.LOADED,
          financialIndex: financialIndex,
          aiGPTMessage: state.aiGPTMessage.copyWith(
            financialIndex: financialIndex,
            financialStatements: oldData,
          ),
        ));

        _init();
      } else {
        emit(state.copyWith(
          type: AIGPTFinancialStatementsMessageStateType.LOADED,
          financialIndex: financialIndex,
          aiGPTMessage:
              state.aiGPTMessage.copyWith(financialIndex: financialIndex),
        ));
      }
    } else {
      emit(state.copyWith(
        type: AIGPTFinancialStatementsMessageStateType.LOADED,
        financialIndex: financialIndex,
        aiGPTMessage:
            state.aiGPTMessage.copyWith(financialIndex: financialIndex),
      ));

      _init();
    }
  }

  void onExpanded(bool isExpanded) {
    emit(state.copyWith(
      type: AIGPTFinancialStatementsMessageStateType.LOADED,
      isExpanded: isExpanded,
    ));
  }

  void onIncomeStatementExpanded(bool isExpanded) {
    emit(state.copyWith(
      type: AIGPTFinancialStatementsMessageStateType.LOADED,
      isIncomeStatementExpanded: isExpanded,
    ));
  }

  void onIncomeStatementViewMore(bool isViewMore) {
    emit(state.copyWith(
      type: AIGPTFinancialStatementsMessageStateType.LOADED,
      isIncomeStatementViewMore: isViewMore,
    ));
  }

  void onBalanceSheetExpanded(bool isExpanded) {
    emit(state.copyWith(
      type: AIGPTFinancialStatementsMessageStateType.LOADED,
      isBalanceSheetExpanded: isExpanded,
    ));
  }

  void onBalanceSheetViewMore(bool isViewMore) {
    emit(state.copyWith(
      type: AIGPTFinancialStatementsMessageStateType.LOADED,
      isBalanceSheetViewMore: isViewMore,
    ));
  }

  void onCashFlowStatementExpanded(bool isExpanded) {
    emit(state.copyWith(
      type: AIGPTFinancialStatementsMessageStateType.LOADED,
      isCashFlowStatementExpanded: isExpanded,
    ));
  }

  void onCashFlowStatementViewMore(bool isViewMore) {
    emit(state.copyWith(
      type: AIGPTFinancialStatementsMessageStateType.LOADED,
      isCashFlowStatementViewMore: isViewMore,
    ));
  }

  void onCellSelected(StatementSheetExcelCell statementCell) async {
    emit(state.copyWith(
      type: AIGPTFinancialStatementsMessageStateType.LOADED,
      statementCellSelected: statementCell,
    ));

    Map<String, String>? firstGrowthMap;
    Map<String, String>? secondGrowthMap;

    if (ListUtility.isEmpty(state.aiGPTMessage.financialStatements)) return;
    var financialStatements = state
        .aiGPTMessage.financialStatements![state.aiGPTMessage.financialIndex];

    var dataMap = financialStatements.annualIncomeData;
    if (state.taxIndex == 0) {
      if (statementCell.statementSheetType ==
          StatementSheetType.INCOME_STATEMENT) {
        dataMap = financialStatements.annualIncomeData;
        firstGrowthMap = financialStatements.incomeAvgGrowth3Y;
        secondGrowthMap = financialStatements.incomeAvgGrowth5Y;
      } else if (statementCell.statementSheetType ==
          StatementSheetType.BALANCE_SHEET) {
        dataMap = financialStatements.annualBalanceData;
        firstGrowthMap = financialStatements.balanceAvgGrowth3Y;
        secondGrowthMap = financialStatements.balanceAvgGrowth5Y;
      } else if (statementCell.statementSheetType ==
          StatementSheetType.CASH_FLOW_STATEMENT) {
        dataMap = financialStatements.annualCashFlowData;
        firstGrowthMap = financialStatements.cashFlowAvgGrowth3Y;
        secondGrowthMap = financialStatements.cashFlowAvgGrowth5Y;
      }
    } else {
      if (statementCell.statementSheetType ==
          StatementSheetType.INCOME_STATEMENT) {
        dataMap = financialStatements.quarterlyIncomeData;
        firstGrowthMap = financialStatements.incomeAvgGrowth3Q;
        secondGrowthMap = financialStatements.incomeAvgGrowth5Q;
      } else if (statementCell.statementSheetType ==
          StatementSheetType.BALANCE_SHEET) {
        dataMap = financialStatements.quarterlyBalanceData;
        firstGrowthMap = financialStatements.balanceAvgGrowth3Q;
        secondGrowthMap = financialStatements.balanceAvgGrowth5Q;
      } else if (statementCell.statementSheetType ==
          StatementSheetType.CASH_FLOW_STATEMENT) {
        dataMap = financialStatements.quarterlyCashFlowData;
        firstGrowthMap = financialStatements.cashFlowAvgGrowth3Q;
        secondGrowthMap = financialStatements.cashFlowAvgGrowth5Q;
      }
    }
    if (dataMap == null || MapUtility.isEmpty(dataMap)) {
      emit(state.resetChartData());
      return;
    }

    await Future.delayed(const Duration(milliseconds: 1000));

    emit(state.copyWith(
        chartData: getChartData(
      statementCell: statementCell,
      dataMap: dataMap,
      isQuarterly: state.taxIndex == 1,
      firstGrowthMap: unwrap(firstGrowthMap, {}),
      secondGrowthMap: unwrap(secondGrowthMap, {}),
    )));
  }

  AIGPTFinancialStatementsChart? getChartData({
    required StatementSheetExcelCell statementCell,
    required Map<String, Map<String, AIGPTFinancialStatementsDataResponse>>
        dataMap,
    required bool isQuarterly,
    required Map<String, String> firstGrowthMap,
    required Map<String, String> secondGrowthMap,
  }) {
    final barGroups = <BarChartGroupData>[];
    final chartValues = <AIGPTFinancialStatementsChartDataPoint>[];
    final sortedDataMap = Map.fromEntries(dataMap.entries.toList()
      ..sort(
        (a, b) {
          final aKey = a.key;
          final bKey = b.key;

          if (aKey == bKey) return 0;

          // sort by year
          if (aKey.length > 4 && bKey.length > 4) {
            return aKey.compareTo(bKey);
          }

          return NumUtility.parseDouble(aKey)
              .compareTo(NumUtility.parseDouble(bKey));
        },
      ));

    double? min = 0;
    double? max;
    String? lastValue;
    var index = 0;
    for (final entry in sortedDataMap.entries) {
      final year = entry.key;
      final statementMap = entry.value;
      final financialStatement = statementMap[statementCell.dataType];
      final value = NumUtility.parseDouble(financialStatement?.value);
      final humanValue =
          NumUtility.formatHumanNumber(financialStatement?.value);

      if (min == null) {
        min = value;
      } else if (min > value) {
        min = value;
      }
      if (max == null) {
        max = value;
      } else if (max < value) {
        max = value;
      }
      if (lastValue == null || StringUtility.isNotEmpty(humanValue)) {
        lastValue = humanValue;
      }

      barGroups.add(BarChartGroupData(
        x: index,
        barRods: [
          BarChartRodData(
            y: value,
            colors: [ColorTheme.g0266FF],
            width: 24,
            borderRadius: BorderRadius.circular(2),
          ),
        ],
      ));
      chartValues.add(AIGPTFinancialStatementsChartDataPoint(
        xDisplay: '$year',
        yDisplay: humanValue,
      ));

      index++;
    }

    if (unwrap(max, 0) == 0) {
      min = 0;
      max = 5;
    }

    var interval = (unwrap(max, 0) - unwrap(min, 0)) /
        (barGroups.isNotEmpty ? barGroups.length : 1);

    var firstGrowthField = unwrap(firstGrowthMap[statementCell.dataType], '-');
    var secondGrowthField =
        unwrap(secondGrowthMap[statementCell.dataType], '-');
    if (StringUtility.isNotEmpty(firstGrowthField) &&
        firstGrowthField != '-' &&
        NumUtility.isNumber(firstGrowthField)) {
      firstGrowthField = '${NumUtility.parseDouble(firstGrowthField) * 100}';
      firstGrowthField = NumUtility.displayWithDecimal(firstGrowthField, 2);
    }
    if (StringUtility.isNotEmpty(secondGrowthField) &&
        secondGrowthField != '-' &&
        NumUtility.isNumber(secondGrowthField)) {
      secondGrowthField = '${NumUtility.parseDouble(secondGrowthField) * 100}';
      secondGrowthField = NumUtility.displayWithDecimal(secondGrowthField, 2);
    }

    return AIGPTFinancialStatementsChart(
      max: unwrap(max, 0),
      min: unwrap(min, 0),
      barGroups: barGroups,
      dataPoints: chartValues,
      lastValue: StringUtility.isEmpty(lastValue) ? '-' : unwrap(lastValue, ''),
      interval: interval.abs(),
      firstGrowthField: firstGrowthField,
      secondGrowthField: secondGrowthField,
    );
  }
}
