import 'package:ai/screens/ai_gpt/widgets/widgets.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';
import 'package:html/dom.dart' as dom;
import 'package:shared_data/shared_data.dart';
import 'package:shared_ui/shared_ui.dart';

// 全局 WebView 缓存，用于存储已创建的 WebView 实例
class WebViewCache {
  // 构造函数
  WebViewCache._internal();
  factory WebViewCache() => _instance;

  // 单例实例
  static final WebViewCache _instance = WebViewCache._internal();

  // 存储 WebView 实例的映射表
  final Map<String, Widget> _cachedWebViews = {};

  // 获取或创建 WebView
  Widget getOrCreateWebView(String url, {double height = 300}) {
    // 如果缓存中已存在该 URL 的 WebView，则直接返回
    if (_cachedWebViews.containsKey(url)) {
      return _cachedWebViews[url]!;
    }

    // 否则创建一个新的 WebView 并缓存它
    final webView = SizedBox(
      height: height,
      child: InAppWebView(
        key: GlobalKey(), // 使用 GlobalKey 确保 WebView 不会被重建
        initialUrlRequest: URLRequest(
          url: WebUri.uri(Uri.parse(url)),
        ),
        initialSettings: InAppWebViewSettings(
          supportZoom: false,
          preferredContentMode: UserPreferredContentMode.MOBILE,
          useShouldOverrideUrlLoading: true,
          javaScriptCanOpenWindowsAutomatically: true,
          useHybridComposition: false,
          cacheEnabled: true, // 启用缓存
          applicationNameForUserAgent: 'AIGPTApp', // 设置用户代理
        ),
        onPermissionRequest: (controller, permissionRequest) async {
          return PermissionResponse(
            resources: permissionRequest.resources,
            action: PermissionResponseAction.GRANT,
          );
        },
        shouldOverrideUrlLoading: (controller, navigationAction) async {
          return Future.value(NavigationActionPolicy.ALLOW);
        },
      ),
    );

    // 缓存 WebView
    _cachedWebViews[url] = webView;
    return webView;
  }

  // 清除缓存
  void clear() {
    _cachedWebViews.clear();
  }
}

class AIGPTMarkdownHtmlView extends StatelessWidget {
  const AIGPTMarkdownHtmlView({super.key, required this.content});

  final String content;
  @override
  Widget build(BuildContext context) {
    final htmlKey = const Key('AI_chat_answer_markdown');

    return LayoutBuilder(
      builder: (context, constraints) => HtmlWidget(
        content,
        key: htmlKey,
        textStyle: FontStyles.Regular16.apply(color: ColorTheme.neutral2),
        customWidgetBuilder: (element) => _buildHtmlCustomWidgetBuilder(
          context: context,
          element: element,
          screenWidth: constraints.maxWidth,
          screenHeight: constraints.maxHeight,
        ),
        customStylesBuilder: (element) {
          if (element.localName == 'html' ||
              element.localName == 'body' ||
              element.localName == 'ul') {
            return {'margin': '0', 'padding': '0'};
          } else if (element.localName == 'p' || element.localName == 'span') {
            return {
              'margin': '8px 0',
            };
          } else if (element.localName == 'li') {
            return {
              'margin': '0',
              'padding-left': '24px',
              'padding-right': '24px',
              'padding-bottom': '8px'
            };
          } else if (element.localName == 'a') {
            final href = unwrap(element.attributes['href'], '');
            if (StringUtility.isEmpty(href)) {
              return {'color': '#00040C'};
            }

            final uri = Uri.parse(href);
            final type = uri.queryParameters['type'];
            if (!StringUtility.equalsIgnoreCase(uri.host, 'localhost.com') ||
                !StringUtility.equalsIgnoreCase(type, 'tag')) {
              return {'color': '#2D69FF'};
            }

            return {'color': '#2D69FF'};
          } else if (element.className == 'mention') {
            return {'color': '#115695'};
          } else if (element.localName == 'ol') {
            return {'margin': '0'};
          } else if (element.localName == 'h1') {
            return {'margin': '18px 0', 'padding': '0', 'font-size': '24px'};
          } else if (element.localName == 'h2') {
            return {'margin': '18px 0', 'padding': '0', 'font-size': '22px'};
          } else if (element.localName == 'h3') {
            return {'margin': '18px 0', 'padding': '0', 'font-size': '20px'};
          }

          return null;
        },
        onTapUrl: (url) {
          AppLauncher.launchURL(url);
          return true;
        },
        onTapImage: (imageMedia) {
          debugPrint('imageMedias $imageMedia');
        },
      ),
    );
  }

  Widget? _buildHtmlCustomWidgetBuilder({
    required BuildContext context,
    required dom.Element element,
    required double screenWidth,
    required double screenHeight,
  }) {
    if (StringUtility.equalsIgnoreCase(element.localName, 'iframe')) {
      final webViewUrl =
          'https://gogpt.dev.addxgo.io/bar-chart4.html?labels=%E4%B8%80%E6%9C%88,%E4%BA%8C%E6%9C%88,%E4%B8%89%E6%9C%88,%E5%9B%9B%E6%9C%88&data=12,18,6,9';
      return RepaintBoundary(
        child: WebViewCache().getOrCreateWebView(webViewUrl, height: 500),
      );
    } else if (element.localName == 'table') {
      return Container(
        width: double.infinity,
        child: AIMarkdownHtmlTableWidget(element.outerHtml),
      );
    }

    return null;
  }
}
