import 'package:ai/screens/ai_gpt/widgets/widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:markdown/markdown.dart' as markdown;
import 'package:shared_data/shared_data.dart';
import 'package:shared_ui/shared_ui.dart';

class AIGPTCustomMarkdown extends StatelessWidget {
  const AIGPTCustomMarkdown({
    Key? key,
    required this.data,
  }) : super(key: key);

  final String data;

  @override
  Widget build(BuildContext context) {
    var testData =
        '''# Japan Large Cap Stocks (P/E > 50): Pros, Cons & Best Picks  
*As of May 28, 2025*

---

## 1. Nintendo Co., Ltd. (7974.T)
- **Market Cap:** <span style="color: #0266FF"><strong>\$142.1B</strong></span>
- **P/E Ratio:** <span style="color: #0266FF"><strong>51.0</strong></span>
- **EPS:** <span style="color: #16D964"><strong>¥239.3</strong></span>
- **Dividend Yield:** <span style="color: #0266FF"><strong>1.0%</strong></span>
- **Current Price:** <span style="color: #0266FF"><strong>¥12,205</strong></span>
- **Target Mean Price:** <span style="color: #0266FF"><strong>¥12,395.80</strong></span>
- **Analyst Recommendation:** <span style="color: #16D964"><strong>Buy</strong></span> (25 opinions)
- **Valuation:** <span style="color: #FB465D"><strong>Highly Overvalued</strong></span>

**Pros:**
- Global gaming leader, strong IP portfolio.
- Buy-rated, target price slightly above current.
- Consistent profitability.

**Cons:**
- Highly overvalued, low yield.
- Dependent on hit-driven product cycles.

---

## 2. Takeda Pharmaceutical Company Limited (4502.T)
- **Market Cap:** <span style="color: #0266FF"><strong>\$65.6B</strong></span>
- **P/E Ratio:** <span style="color: #0266FF"><strong>62.5</strong></span>
- **EPS:** <span style="color: #16D964"><strong>¥67.23</strong></span>
- **Dividend Yield:** <span style="color: #16D964"><strong>4.79%</strong></span>
- **Current Price:** <span style="color: #0266FF"><strong>¥4,200</strong></span>
- **Target Mean Price:** <span style="color: #0266FF"><strong>¥4,982.06</strong></span>
- **Analyst Recommendation:** <span style="color: #16D964"><strong>Buy</strong></span> (17 opinions)
- **Valuation:** <span style="color: #FB465D"><strong>Highly Overvalued</strong></span>

**Pros:**
- Strong global pharma pipeline, high yield.
- Buy-rated, target price above current.

**Cons:**
- Highly overvalued, patent/regulatory risks.
- Pharma sector volatility.

---

## 3. Aeon Co., Ltd. (8267.T)
- **Market Cap:** <span style="color: #0266FF"><strong>\$37.9B</strong></span>
- **P/E Ratio:** <span style="color: #0266FF"><strong>130.9</strong></span>
- **EPS:** <span style="color: #16D964"><strong>¥33.75</strong></span>
- **Dividend Yield:** <span style="color: #0266FF"><strong>0.91%</strong></span>
- **Current Price:** <span style="color: #0266FF"><strong>¥4,419</strong></span>
- **Target Mean Price:** <span style="color: #0266FF"><strong>¥3,164.29</strong></span>
- **Analyst Recommendation:** None (7 opinions)
- **Valuation:** <span style="color: #16D964"><strong>Undervalued</strong></span>

**Pros:**
- Major retailer, defensive sector.
- Undervalued tag, stable cash flows.

**Cons:**
- Extremely high P/E, low yield.
- Target price below current, no buy rating.

---

## 4. Ajinomoto Co., Inc. (2802.T)
- **Market Cap:** <span style="color: #0266FF"><strong>\$35.2B</strong></span>
- **P/E Ratio:** <span style="color: #0266FF"><strong>100.5</strong></span>
- **EPS:** <span style="color: #16D964"><strong>¥34.87</strong></span>
- **Dividend Yield:** <span style="color: #0266FF"><strong>1.38%</strong></span>
- **Current Price:** <span style="color: #0266FF"><strong>¥3,503</strong></span>
- **Target Mean Price:** <span style="color: #0266FF"><strong>¥3,713.64</strong></span>
- **Analyst Recommendation:** <span style="color: #16D964"><strong>Buy</strong></span> (11 opinions)
- **Valuation:** <span style="color: #16D964"><strong>Undervalued</strong></span>

**Pros:**
- Global food and chemical leader.
- Buy-rated, target price above current.

**Cons:**
- Very high P/E, low yield.
- Growth may not justify valuation.

---

## 5. Kyocera Corporation (6971.T)
- **Market Cap:** <span style="color: #0266FF"><strong>\$24.4B</strong></span>
- **P/E Ratio:** <span style="color: #0266FF"><strong>101.5</strong></span>
- **EPS:** <span style="color: #16D964"><strong>¥17.04</strong></span>
- **Dividend Yield:** <span style="color: #16D964"><strong>2.9%</strong></span>
- **Current Price:** <span style="color: #0266FF"><strong>¥1,729</strong></span>
- **Target Mean Price:** <span style="color: #0266FF"><strong>¥1,875.71</strong></span>
- **Analyst Recommendation:** <span style="color: #16D964"><strong>Buy</strong></span> (14 opinions)
- **Valuation:** <span style="color: #16D964"><strong>Deeply Undervalued</strong></span>

**Pros:**
- Diversified tech and ceramics leader.
- Buy-rated, target price above current.

**Cons:**
- Extremely high P/E, moderate yield.
- Earnings growth may not justify valuation.

---

## 6. ENEOS Holdings, Inc. (5020.T)
- **Market Cap:** <span style="color: #0266FF"><strong>\$18.0B</strong></span>
- **P/E Ratio:** <span style="color: #0266FF"><strong>171.0</strong></span>
- **EPS:** <span style="color: #16D964"><strong>¥3.92</strong></span>
- **Dividend Yield:** <span style="color: #16D964"><strong>4.44%</strong></span>
- **Current Price:** <span style="color: #0266FF"><strong>¥670.4</strong></span>
- **Target Mean Price:** <span style="color: #0266FF"><strong>¥924.29</strong></span>
- **Analyst Recommendation:** None (7 opinions)
- **Valuation:** <span style="color: #16D964"><strong>Undervalued</strong></span>

**Pros:**
- Leading energy company, high yield.
- Undervalued, target price above current.

**Cons:**
- Extremely high P/E, volatile sector.
- No buy rating.

---

## 7. MonotaRO Co., Ltd. (3064.T)
- **Market Cap:** <span style="color: #0266FF"><strong>\$15.0B</strong></span>
- **P/E Ratio:** <span style="color: #0266FF"><strong>54.2</strong></span>
- **EPS:** <span style="color: #16D964"><strong>¥55.59</strong></span>
- **Dividend Yield:** <span style="color: #0266FF"><strong>1.07%</strong></span>
- **Current Price:** <span style="color: #0266FF"><strong>¥3,013</strong></span>
- **Target Mean Price:** <span style="color: #0266FF"><strong>¥2,633.00</strong></span>
- **Analyst Recommendation:** <span style="color: #FB465D"><strong>Hold</strong></span> (10 opinions)
- **Valuation:** <span style="color: #FB465D"><strong>Overvalued</strong></span>

**Pros:**
- E-commerce leader in industrial supplies.
- Growth potential in digital B2B.

**Cons:**
- Overvalued, target price below current.
- Hold rating, low yield.

---

## 8. Sumitomo Metal Mining Co., Ltd. (5713.T)
- **Market Cap:** <span style="color: #0266FF"><strong>\$9.4B</strong></span>
- **P/E Ratio:** <span style="color: #0266FF"><strong>57.0</strong></span>
- **EPS:** <span style="color: #16D964"><strong>¥59.86</strong></span>
- **Dividend Yield:** <span style="color: #16D964"><strong>3.93%</strong></span>
- **Current Price:** <span style="color: #0266FF"><strong>¥3,412</strong></span>
- **Target Mean Price:** <span style="color: #0266FF"><strong>¥3,735.56</strong></span>
- **Analyst Recommendation:** <span style="color: #16D964"><strong>Buy</strong></span> (9 opinions)
- **Valuation:** <span style="color: #FB465D"><strong>Highly Overvalued</strong></span>

**Pros:**
- Major mining and materials supplier.
- Buy-rated, target price above current.

**Cons:**
- Highly overvalued, cyclical sector.
- Commodity price risk.

---

## 9. Skylark Holdings Co., Ltd. (3197.T)
- **Market Cap:** <span style="color: #0266FF"><strong>\$7.5B</strong></span>
- **P/E Ratio:** <span style="color: #0266FF"><strong>50.2</strong></span>
- **EPS:** <span style="color: #16D964"><strong>¥65.45</strong></span>
- **Dividend Yield:** <span style="color: #0266FF"><strong>0.74%</strong></span>
- **Current Price:** <span style="color: #0266FF"><strong>¥3,285</strong></span>
- **Target Mean Price:** <span style="color: #0266FF"><strong>¥2,262.50</strong></span>
- **Analyst Recommendation:** <span style="color: #FB465D"><strong>Hold</strong></span> (4 opinions)
- **Valuation:** <span style="color: #0266FF"><strong>Fairly Valued</strong></span>

**Pros:**
- Restaurant chain, consumer recovery play.

**Cons:**
- High P/E, low yield, target price below current.
- Hold rating, limited upside.

---

## 10. Asahi Intecc Co., Ltd. (7747.T)
- **Market Cap:** <span style="color: #0266FF"><strong>\$6.1B</strong></span>
- **P/E Ratio:** <span style="color: #0266FF"><strong>58.0</strong></span>
- **EPS:** <span style="color: #16D964"><strong>¥38.79</strong></span>
- **Dividend Yield:** <span style="color: #16D964"><strong>2.0%</strong></span>
- **Current Price:** <span style="color: #0266FF"><strong>¥2,250</strong></span>
- **Target Mean Price:** <span style="color: #0266FF"><strong>¥3,162.67</strong></span>
- **Analyst Recommendation:** <span style="color: #16D964"><strong>Strong Buy</strong></span> (15 opinions)
- **Valuation:** <span style="color: #16D964"><strong>Deeply Undervalued</strong></span>

**Pros:**
- Medical device innovator, global growth.
- Strong buy rating, target price well above current.

**Cons:**
- High P/E, moderate yield.
- Healthcare sector volatility.

---

# Conclusion: Best Japanese Stocks to Invest (May 2025)

**Top Picks:**
- **Asahi Intecc (7747.T):** Strong buy, deeply undervalued, target price well above current.
- **Ajinomoto (2802.T):** Buy-rated, undervalued, global food/chemical leader.
- **Kyocera (6971.T):** Buy-rated, deeply undervalued, diversified tech.
- **Takeda Pharma (4502.T):** Buy-rated, high yield, global pharma pipeline.

**Caution:**  
- Avoid stocks with target prices below current and/or hold/no buy ratings (e.g., MonotaRO, Skylark, Aeon).
- High P/E ratios mean higher risk if growth expectations are not met.

---

**Disclaimer:** This analysis is for informational purposes only and does not constitute investment advice. Please consult a qualified financial advisor before making investment decisions.

**References:**  
- [Nintendo Co., Ltd. (7974:TYO) Stock Price & News](https://www.google.com/finance/quote/7974:TYO)  
- [Takeda Pharmaceutical Company Limited (4502:TYO) Stock Price & News](https://www.google.com/finance/quote/4502:TYO)  
- [Ajinomoto Co., Inc. (2802:TYO) Stock Price & News](https://www.google.com/finance/quote/2802:TYO)  
- [Asahi Intecc Co., Ltd. (7747:TYO) Stock Price & News](https://www.google.com/finance/quote/7747:TYO)

**Follow-up Questions:**  
- Would you like a deeper dive into the financials or business segments of any Japanese stock?  
- Are you interested in sector-specific trends in Japan?  
- Do you want to compare these Japanese stocks with global peers?''';
    // 处理特定关键词替换
    final processedData = _processMarkdownText(
        testData.replaceAll('```markdown\n', '').replaceAll('```', ''));

    final htmlContent = markdown.markdownToHtml(
      processedData.result,
      extensionSet: markdown.ExtensionSet.gitHubFlavored,
    );

    // log('processedData $htmlContent');

    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        if (processedData.indexValues.isNotEmpty) ...[
          Container(
            margin: const EdgeInsets.only(bottom: 12),
            decoration: BoxDecoration(
                border: Border.all(color: ColorTheme.gEFF0F4, width: 1),
                borderRadius: BorderRadius.circular(8)),
            child: Row(
              children: processedData.indexValues.entries
                  .map(_buildIndexItem)
                  .toList(),
            ),
          ),
        ],
        AIGPTMarkdownHtmlView(
          content: htmlContent,
        ),
      ],
    );
  }

  Widget _buildIndexItem(MapEntry<String, String> index) {
    return Expanded(
        child: Padding(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Container(
            height: 42,
            alignment: Alignment.center,
            child: Text(
              index.key.replaceFirst(' ', '\n'),
              textAlign: TextAlign.center,
              style: FontStyles.Medium14.copyWith(
                color: ColorTheme.g21232E,
                height: 1.2,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(height: 8),
          SvgPicture.asset(_getIndexImgSrc(index)),
          const SizedBox(height: 8),
          Container(
            height: 42,
            alignment: Alignment.center,
            child: Text(
              index.value.replaceFirst(' ', '\n'),
              textAlign: TextAlign.center,
              style: FontStyles.Medium14.copyWith(
                color: _getIndexValueColor(index),
                height: 1.2,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    ));
  }

  String _getIndexImgSrc(MapEntry<String, String> index) {
    var srcIndex = _getIndex(index);
    return getAIIconPath('dashboard_$srcIndex.svg');
  }

  Color _getIndexValueColor(MapEntry<String, String> index) {
    var srcIndex = _getIndex(index);
    if (srcIndex == 1)
      return const Color(0xFFA23B44);
    else if (srcIndex == 2)
      return const Color(0xFFE85561);
    else if (srcIndex == 3)
      return const Color(0xFFEF884C);
    else if (srcIndex == 4)
      return const Color(0xFF65D671);
    else if (srcIndex == 5) return const Color(0xFF45964F);
    return ColorTheme.g0266FF;
  }

  int _getIndex(MapEntry<String, String> index) {
    var srcIndex = 0;
    if (index.key == AIData.MESSAGE_SPECIAL_ANALYST_RATING_FLAG) {
      srcIndex = AnalystRatingType.values.indexWhere((type) => index.value
          .replaceAll(' ', '')
          .contains(EnumToString.convertToString(type)));
    } else if (index.key == AIData.MESSAGE_SPECIAL_TECHNICAL_SENTIMENT_FLAG) {
      srcIndex = TechnicalSentimentType.values.indexWhere((type) => index.value
          .replaceAll(' ', '')
          .contains(EnumToString.convertToString(type)));
    } else if (index.key == AIData.MESSAGE_SPECIAL_VALUATION_FLAG) {
      srcIndex = ValuationType.values.indexWhere((type) => index.value
          .replaceAll(' ', '')
          .contains(EnumToString.convertToString(type)));
    }

    return srcIndex + 1;
  }

  MarkdownResult _processMarkdownText(String text) {
    // 将文本分割成行
    final lines = text.split('\n');
    final processedLines = <String>[];

    // 用于存储提取的值
    String? analystRating;
    String? technicalSentiment;
    String? valuation;
    final indexValues = <String, String>{};
    // 处理每一行
    for (var i = 0; i < lines.length; i++) {
      final line = lines[i];
      if (line.contains('Analyst Rating') && line.contains(':')) {
        analystRating = _extractValueAfterColon(line, 'Analyst Rating');
        analystRating = HtmlUtility.getTextWithoutStyle(analystRating);

        if (StringUtility.isNotEmpty(analystRating) &&
            _getIndex({
                  AIData.MESSAGE_SPECIAL_ANALYST_RATING_FLAG: analystRating
                }.entries.first) >
                0) {
          continue;
        } else {
          analystRating = null;
        }
      } else if (line.contains('Technical Sentiment') && line.contains(':')) {
        technicalSentiment =
            _extractValueAfterColon(line, 'Technical Sentiment');
        technicalSentiment =
            HtmlUtility.getTextWithoutStyle(technicalSentiment);

        if (StringUtility.isNotEmpty(technicalSentiment) &&
            _getIndex({
                  AIData.MESSAGE_SPECIAL_TECHNICAL_SENTIMENT_FLAG:
                      technicalSentiment
                }.entries.first) >
                0) {
          continue;
        } else {
          technicalSentiment = null;
        }
      } else if (line.contains('Valuation') && line.contains(':')) {
        valuation = _extractValueAfterColon(line, 'Valuation');
        valuation = HtmlUtility.getTextWithoutStyle(valuation);

        if (StringUtility.isNotEmpty(valuation) &&
            _getIndex({AIData.MESSAGE_SPECIAL_VALUATION_FLAG: valuation}
                    .entries
                    .first) >
                0) {
          continue;
        } else {
          valuation = null;
        }
      }

      // 将其他行添加到处理后的文本中
      processedLines.add(line);
    }

    // 添加提取的值作为图片标记的参数
    if (analystRating != null) {
      indexValues[AIData.MESSAGE_SPECIAL_ANALYST_RATING_FLAG] = analystRating;
    }

    if (technicalSentiment != null) {
      indexValues[AIData.MESSAGE_SPECIAL_TECHNICAL_SENTIMENT_FLAG] =
          technicalSentiment;
    }

    if (valuation != null) {
      indexValues[AIData.MESSAGE_SPECIAL_VALUATION_FLAG] = valuation;
    }

    return MarkdownResult(
      processedLines.join('\n'),
      indexValues,
    );
  }

  String _extractValueAfterColon(String line, String keyword) {
    // 提取冒号后面的值并去除前后空格
    final colonIndex = line.indexOf(':');
    if (colonIndex != -1 && colonIndex + 1 < line.length) {
      var result = line
          .substring(colonIndex + 1)
          .replaceAll('*', '')
          .split('(')[0]
          .trim();
      return result;
    }
    return '';
  }
}

class MarkdownResult {
  MarkdownResult(
    this.result,
    this.indexValues,
  );
  final String result;
  final Map<String, String> indexValues;
}

enum AnalystRatingType {
  StrongSell,
  Sell,
  Hold,
  Buy,
  StrongBuy,
}

enum TechnicalSentimentType {
  ExtremeBearish,
  Bearish,
  Neutral,
  Bullish,
  ExtremeBullish,
}

enum ValuationType {
  HighlyOvervalued,
  Overvalued,
  FairlyValued,
  Undervalued,
  DeeplyUndervalued,
}
