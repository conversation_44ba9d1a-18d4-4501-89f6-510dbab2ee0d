import 'dart:io';

import 'package:addx_go_wallet/bloc/blocs.dart';
import 'package:ai/ai.dart';
import 'package:ai/screens/ai_gpt/widgets/ai_gpt_message_view.dart';
import 'package:ai/screens/ai_gpt/widgets/widgets.dart';
import 'package:appinio_social_share/appinio_social_share.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:share_plus/share_plus.dart';
import 'package:shared_data/shared_data.dart';
import 'package:shared_service/shared_service.dart';
import 'package:shared_ui/shared_ui.dart';
import 'package:translator/translators.dart';
import 'package:widgets_to_image/widgets_to_image.dart';

class AIGPTShareMessagesQrCodeModelView extends StatefulWidget {
  const AIGPTShareMessagesQrCodeModelView({
    super.key,
    required this.heightView,
    required this.messages,
  });

  final double heightView;
  final List<AIGPTMessage> messages;

  static void showModelView(
      BuildContext parentContext, List<AIGPTMessage> messages) async {
    await showModalBottomSheet(
      isScrollControlled: true,
      useSafeArea: false,
      context: parentContext,
      builder: (context) => BlocProvider<AIGPTShareMessagesQrCodeCubit>(
        create: (context) => AIGPTShareMessagesQrCodeCubit(
          authCubit: BlocProvider.of<AuthCubit>(context),
          authRepository: RepositoryProvider.of<AuthRepository>(context),
          platformHelper: PlatformHelper(),
          title: DateTime.now().millisecond.toString(),
        ),
        child: AIGPTShareMessagesQrCodeModelView(
          heightView: MediaQuery.of(parentContext).size.height,
          messages: messages,
        ),
      ),
      backgroundColor: Colors.transparent,
    );
  }

  @override
  State<AIGPTShareMessagesQrCodeModelView> createState() =>
      _AIGPTShareMessagesQrCodeModelViewState();
}

class _AIGPTShareMessagesQrCodeModelViewState
    extends State<AIGPTShareMessagesQrCodeModelView> {
  final fToast = FToast();
  final widgetToImageController = WidgetsToImageController();

  double get verticalPadding => widget.heightView * 0.025;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _exposeImage(false);
    });
  }

  @override
  Widget build(BuildContext context) {
    fToast.init(context);

    return BlocConsumer<AIGPTShareMessagesQrCodeCubit,
        AIGPTShareMessagesQrCodeState>(
      listener: (context, state) async {
        // debugPrint('state.type ${state.type}');
        if (state.type ==
            AIGPTShareMessagesQrCodeStateType.SAVE_FILE_TO_DOWNLOAD_SUCCESS) {
          showToast(TranslatorService.translate('__GPTScreen_Saved_Label__'));
        } else if (state.type == AIGPTShareMessagesQrCodeStateType.ERROR) {
          showToast(unwrap(
            ExceptionHelper.getMessage(state.exception),
            TranslatorService.translate('__GPTScreen_SaveError_Label__'),
          ));
        } else if (state.type == AIGPTShareMessagesQrCodeStateType.SAVED) {
          _handleSocialShare(state);
        }
      },
      builder: (context, state) => LoadingOverlayView(
        isLoading: state.type == AIGPTShareMessagesQrCodeStateType.LOADING,
        child: StatusBarView(
          statusBarKey:
              const Key('status_bar_view_share_message_qr_code_model_view'),
          statusBarColor: Colors.transparent,
          navigationBarColor: ColorTheme.neutral100,
          child: Stack(
            children: [
              // Hidden widget for screenshot - positioned off-screen
              Positioned(
                left: -10000, // Move off-screen
                top: 0,
                child: WidgetsToImage(
                  child: _buildModalWrapper(state),
                  controller: widgetToImageController,
                ),
              ),
              Container(
                color: Colors.white,
                child: Container(
                  height: widget.heightView,
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                      colors: [
                        ColorTheme.r2g102b255a01, // #0266FF
                        ColorTheme.r121g26b255a01, // #791AFF
                      ],
                    ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      SizedBox(height: verticalPadding),
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 24),
                          child: _buildDisplayView(state),
                        ),
                      ),
                      SizedBox(height: verticalPadding),
                      _buildShareMenu(state)
                    ],
                  ),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDisplayView(AIGPTShareMessagesQrCodeState state) {
    final screenWidth = MediaQuery.of(context).size.width;

    return LayoutBuilder(
      builder: (context, constraints) {
        return Container(
          constraints: BoxConstraints(
            maxHeight: constraints.maxHeight,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(16),
                child: Container(
                  padding: EdgeInsets.zero,
                  width: screenWidth * 0.85,
                  decoration: const BoxDecoration(
                    color: ColorTheme.neutral100,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Image.asset(
                        getAIIconPath('share_header_background.png'),
                        width: double.infinity,
                        fit: BoxFit.fill,
                      ),
                      const SizedBox(height: 12),
                      Container(
                        constraints: BoxConstraints(
                          maxHeight: constraints.maxHeight - 262,
                        ),
                        child: SingleChildScrollView(
                          child: _buildContentWidget(),
                        ),
                      ),
                      const SizedBox(height: 12),
                      const CustomDividerView(
                        padding: EdgeInsets.symmetric(horizontal: 16),
                      ),
                      const SizedBox(height: 8),
                      _buildCompanyInfo(),
                      const SizedBox(height: 12),
                    ],
                  ),
                ),
              )
            ],
          ),
        );
      },
    );
  }

  Widget _buildModalWrapper(AIGPTShareMessagesQrCodeState state) {
    final screenWidth = MediaQuery.of(context).size.width;

    return Material(
      color: Colors.transparent,
      child: Container(
        // Use intrinsic dimensions to avoid cutting off content
        constraints: BoxConstraints(
          minWidth: screenWidth * 0.9,
          maxWidth: screenWidth * 2.0, // Allow wider content
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: EdgeInsets.zero,
            decoration: const BoxDecoration(
              color: ColorTheme.neutral100,
            ),
            child: IntrinsicWidth(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Image.asset(
                    getAIIconPath('share_header_background.png'),
                    width: double.infinity,
                    fit: BoxFit.fill,
                  ),
                  const SizedBox(height: 6),
                  _buildContentWidget(),
                  const SizedBox(height: 6),
                  const CustomDividerView(
                    padding: EdgeInsets.symmetric(horizontal: 16),
                  ),
                  const SizedBox(height: 8),
                  _buildCompanyInfo(),
                  const SizedBox(height: 12),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildContentWidget() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: List.generate(
          widget.messages.length,
          (index) {
            final gptMessage = widget.messages[index];
            final isLastMessage = index == widget.messages.length - 1;

            return Container(
              // Ensure each message has proper constraints
              constraints: const BoxConstraints(
                minWidth: 300, // Minimum width to prevent cramping
              ),
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 12),
                child: AIGPTMessageView(
                  gptMessage: gptMessage,
                  processingMessage: null,
                  isLastMessage: isLastMessage,
                  fToast: fToast,
                  onAddMessage: (_, {required msgType, isQuestion}) {},
                  isSharingEnable: false,
                  isMessageShareSelected: false,
                  isViewOnly: true,
                  onMessageShareTapped: () {},
                  onShareTapped: () {},
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildCompanyInfo() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Image.asset(
        getAIIconPath('share_footer_background.jpg'),
        width: double.infinity,
        fit: BoxFit.fill,
      ),
    );
  }

  Widget _buildShareMenu(AIGPTShareMessagesQrCodeState state) {
    final filePath = unwrap(state.filePath, '');

    return ClipRRect(
      borderRadius: const BorderRadius.only(
        topLeft: Radius.circular(16),
        topRight: Radius.circular(16),
      ),
      child: Container(
        color: Colors.white,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      TranslatorService.translate(
                          '__Wallet_Receive_Share_Title__'),
                      style:
                          FontStyles.SemiBold16.apply(color: ColorTheme.dark1),
                    ),
                    GestureDetector(
                      onTap: () {
                        Navigator.of(context).pop();
                      },
                      child: SvgPicture.asset(
                        getIconPath('modal_close.svg'),
                        width: 24,
                        height: 24,
                      ),
                    )
                  ],
                ),
              ),
              const SizedBox(height: 24),
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.symmetric(horizontal: 6),
                child: Row(
                  children: [
                    _buildItem(
                      title: TranslatorService.translate(
                          '__CommunityScreen_Whatsapp_Label__'),
                      icon: getCommunityIconPath('whatsapp.png'),
                      onPressed: () {
                        _exposeImage(
                          true,
                          delayMillisecondsTimes: 1000,
                          socialShareMessageType:
                              AIGPTSocialShareMessageType.WHATSAPP,
                        );
                      },
                      isSvgIcon: false,
                      isFullSize: true,
                    ),
                    _buildItem(
                      title: TranslatorService.translate(
                          '__CommunityScreen_Facebook_Label__'),
                      icon: getCommunityIconPath('facebook.png'),
                      onPressed: () {
                        _exposeImage(
                          true,
                          delayMillisecondsTimes: 1000,
                          socialShareMessageType:
                              AIGPTSocialShareMessageType.FACEBOOK,
                        );
                      },
                      isSvgIcon: false,
                      isFullSize: true,
                    ),
                    _buildItem(
                      title: TranslatorService.translate(
                          '__CommunityScreen_X_Label__'),
                      icon: getCommunityIconPath('x.png'),
                      onPressed: () {
                        _exposeImage(
                          true,
                          delayMillisecondsTimes: 1000,
                          socialShareMessageType: AIGPTSocialShareMessageType.X,
                        );
                      },
                      isSvgIcon: false,
                      isFullSize: true,
                    ),
                    _buildItem(
                      title: TranslatorService.translate(
                          '__CommunityScreen_Telegram_Label__'),
                      icon: getCommunityIconPath('telegram.png'),
                      onPressed: () {
                        _exposeImage(
                          true,
                          delayMillisecondsTimes: 1000,
                          socialShareMessageType:
                              AIGPTSocialShareMessageType.TELEGRAM,
                        );
                      },
                      isSvgIcon: false,
                      isFullSize: true,
                    ),
                    Container(
                      width: 80,
                      height: 108,
                      child: ShareButtonView(
                        targetType: ShareButtonViewType.SHARE,
                        filePath: filePath,
                        title: state.title,
                        contentSize: 56,
                        iconSize: 28,
                        spacing: 6,
                        textStyle:
                            FontStyles.Regular12.apply(color: ColorTheme.grey1),
                      ),
                    ),
                    Container(
                      width: 80,
                      height: 108,
                      child: ShareButtonView(
                        targetType: ShareButtonViewType.SAVE,
                        filePath: filePath,
                        title: state.title,
                        onTapped: () {
                          if (StringUtility.isNotEmpty(filePath)) {
                            aiGPTShareMessagesQrCodeCubit
                                .saveFileToDownload(filePath);
                          } else {
                            _exposeImage(
                              true,
                              delayMillisecondsTimes: 1000,
                              requiredSaveFileToDownload: true,
                            );
                          }
                        },
                        contentSize: 56,
                        iconSize: 28,
                        spacing: 6,
                        textStyle:
                            FontStyles.Regular12.apply(color: ColorTheme.grey1),
                      ),
                    )
                  ],
                ),
              ),
              SizedBox(height: ViewUtility.getBottomPadding(context))
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildItem({
    required String title,
    required String icon,
    required Function() onPressed,
    double iconSize = 24.0,
    bool isSvgIcon = true,
    bool isFullSize = false,
  }) {
    return SizedBox(
      width: 80,
      height: 108,
      child: CircleButtonView(
        size: 56,
        iconSize: iconSize,
        backgroundColor: ColorTheme.white,
        allowToPop: false,
        icon: icon,
        text: title,
        isSvgIcon: isSvgIcon,
        isFullSize: isFullSize,
        onPressed: onPressed,
      ),
    );
  }

  void showToast(String message) {
    showToastInfoAtCenter(
      context,
      fToast,
      message,
      backgroundColor: Colors.black,
    );
  }

  void _exposeImage(
    bool isBlockUI, {
    int delayMillisecondsTimes = 3500,
    bool requiredSaveFileToDownload = false,
    AIGPTSocialShareMessageType? socialShareMessageType,
  }) async {
    if (isBlockUI) {
      aiGPTShareMessagesQrCodeCubit.showLoading();
    }

    // Ensure widget is fully rendered before capture
    await Future.delayed(Duration(milliseconds: delayMillisecondsTimes));

    try {
      // Force a rebuild to ensure layout is complete
      if (mounted) {
        setState(() {});
        await Future.delayed(const Duration(milliseconds: 100));
      }

      // Capture the image
      final imgData = await widgetToImageController.capture();

      if (imgData != null) {
        aiGPTShareMessagesQrCodeCubit.exportFile(
          imgData,
          isBlockUI: isBlockUI,
          requiredSaveFileToDownload: requiredSaveFileToDownload,
          socialShareMessageType: socialShareMessageType,
        );
      } else {
        debugPrint('_exposeImage: Failed to capture image - imgData is null');
        // Handle error case - you may need to implement error handling
      }
    } catch (e) {
      debugPrint('_exposeImage error: $e');
      // Handle error case - you may need to implement error handling
    }
  }

  void _handleSocialShare(AIGPTShareMessagesQrCodeState state) async {
    final filePath = unwrap(state.filePath, '');
    if (state.socialShareMessageType == AIGPTSocialShareMessageType.FACEBOOK) {
      if (await AppLauncher.isFacebookInstalled()) {
        if (Platform.isIOS) {
          await AppinioSocialShare().iOS.shareToFacebook(
            TranslatorService.translate(
                '__GPTScreen_AIGenerateContent_Label__'),
            getIt<Environment>().website,
            [unwrap(state.filePath, '')],
          );
        } else {
          await AppinioSocialShare().android.shareToFacebook(
            TranslatorService.translate(
                '__GPTScreen_AIGenerateContent_Label__'),
            getIt<Environment>().website,
            [filePath],
          );
        }
      } else {
        AppLauncher.openFacebookAppStore();
      }
    } else if (state.socialShareMessageType ==
        AIGPTSocialShareMessageType.TELEGRAM) {
      if (await AppLauncher.isTelegramInstalled()) {
        if (Platform.isIOS) {
          await Share.shareXFiles([XFile(filePath)]);
        } else {
          await AppinioSocialShare().android.shareToTelegram(
              TranslatorService.translate(
                  '__GPTScreen_AIGenerateContent_Label__'),
              filePath);
        }
      } else {
        AppLauncher.openTelegramAppStore();
      }
    } else if (state.socialShareMessageType == AIGPTSocialShareMessageType.X) {
      if (await AppLauncher.isTwitterInstalled()) {
        if (Platform.isIOS) {
          await AppinioSocialShare().iOS.shareToTwitter(
              TranslatorService.translate(
                  '__GPTScreen_AIGenerateContent_Label__'),
              filePath);
        } else {
          await AppinioSocialShare().android.shareToTwitter(
              TranslatorService.translate(
                  '__GPTScreen_AIGenerateContent_Label__'),
              filePath);
        }
      } else {
        AppLauncher.openTwitterAppStore();
      }
    } else if (state.socialShareMessageType ==
        AIGPTSocialShareMessageType.WHATSAPP) {
      if (await AppLauncher.isWhatsappInstalled()) {
        if (Platform.isIOS) {
          await AppinioSocialShare().iOS.shareImageToWhatsApp(filePath);
        } else {
          await AppinioSocialShare().android.shareFilesToWhatsapp([filePath]);
        }
      } else {
        AppLauncher.openWhatsappAppStore();
      }
    }
  }

  AIGPTShareMessagesQrCodeCubit get aiGPTShareMessagesQrCodeCubit =>
      BlocProvider.of<AIGPTShareMessagesQrCodeCubit>(context);
}
