import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

class LongWidgetImageRenderer {
  final double width;
  final double segmentHeight;
  final double pixelRatio;

  LongWidgetImageRenderer({
    this.width = 1080,
    this.segmentHeight = 2000,
    this.pixelRatio = 3.0,
  });

  /// 主入口：自动测量+分页渲染
  Future<Uint8List?> render(Widget widget) async {
    final fullHeight = await _measureWidgetHeight(widget);
    return await _renderPaged(widget, fullHeight);
  }

  /// 步骤1：测量 widget 的总高度
  Future<double> _measureWidgetHeight(Widget widget) async {
    final boundary = RenderRepaintBoundary();

    final renderView = RenderView(
      window: WidgetsBinding.instance.platformDispatcher.views.first,
      child: RenderPositionedBox(
        alignment: Alignment.topLeft,
        child: boundary,
      ),
      configuration: ViewConfiguration(
        size: Size(width, 10000),
        devicePixelRatio: pixelRatio,
      ),
    );

    final pipelineOwner = PipelineOwner();
    final buildOwner = BuildOwner(focusManager: FocusManager());

    final element = RenderObjectToWidgetAdapter<RenderBox>(
      container: boundary,
      child: _wrapWithContext(
        _wrapWidgetForRender(widget, 10000),
      ),
    ).attachToRenderTree(buildOwner);

    pipelineOwner.rootNode = renderView;
    renderView.prepareInitialFrame();

    buildOwner.buildScope(element);
    buildOwner.finalizeTree();
    pipelineOwner.flushLayout();

    return boundary.size.height;
  }

  /// 步骤2：分页渲染+合成图像
  Future<Uint8List?> _renderPaged(Widget widget, double fullHeight) async {
    final segments = <ui.Image>[];
    final segmentCount = (fullHeight / segmentHeight).ceil();

    for (int i = 0; i < segmentCount; i++) {
      final segment = await _renderSegment(
        widget,
        offsetY: -i * segmentHeight,
        size: Size(width, segmentHeight),
      );
      if (segment != null) {
        segments.add(segment);
      }
    }

    final fullImage = await _combineSegments(segments);
    final byteData = await fullImage.toByteData(format: ui.ImageByteFormat.png);
    return byteData?.buffer.asUint8List();
  }

  /// 渲染单个分页
  Future<ui.Image?> _renderSegment(
    Widget widget, {
    required Size size,
    required double offsetY,
  }) async {
    final boundary = RenderRepaintBoundary();

    final renderView = RenderView(
      window: WidgetsBinding.instance.platformDispatcher.views.first,
      child: RenderPositionedBox(
        alignment: Alignment.topLeft,
        child: boundary,
      ),
      configuration: ViewConfiguration(
        size: size,
        devicePixelRatio: pixelRatio,
      ),
    );

    final pipelineOwner = PipelineOwner();
    final buildOwner = BuildOwner(focusManager: FocusManager());

    final translatedWidget = Transform.translate(
      offset: Offset(0, offsetY),
      child: _wrapWidgetForRender(widget, segmentHeight * 2),
    );

    final element = RenderObjectToWidgetAdapter<RenderBox>(
      container: boundary,
      child: _wrapWithContext(translatedWidget),
    ).attachToRenderTree(buildOwner);

    pipelineOwner.rootNode = renderView;
    renderView.prepareInitialFrame();

    buildOwner.buildScope(element);
    buildOwner.finalizeTree();

    pipelineOwner.flushLayout();
    pipelineOwner.flushCompositingBits();
    pipelineOwner.flushPaint();

    return await boundary.toImage(pixelRatio: pixelRatio);
  }

  /// 拼接多个 segment 为一张图
  Future<ui.Image> _combineSegments(List<ui.Image> images) async {
    int totalHeight = images.fold(0, (sum, img) => sum + img.height);
    int width = images.first.width;

    final recorder = ui.PictureRecorder();
    final canvas = ui.Canvas(recorder);

    double offsetY = 0;
    for (final img in images) {
      canvas.drawImage(img, Offset(0, offsetY), Paint());
      offsetY += img.height.toDouble();
    }

    final picture = recorder.endRecording();
    return await picture.toImage(width, totalHeight);
  }

  /// 包装 widget，避免 overflow 报错（不再使用 IntrinsicHeight）
  Widget _wrapWidgetForRender(Widget child, double height) {
    return SizedBox(
      height: height,
      child: OverflowBox(
        alignment: Alignment.topCenter,
        minHeight: 0,
        maxHeight: double.infinity,
        child: Align(
          alignment: Alignment.topCenter,
          child: child,
        ),
      ),
    );
  }

  /// 注入 MediaQuery 和 Directionality 环境
  Widget _wrapWithContext(Widget child) {
    return MediaQuery(
      data: MediaQueryData(
        size: Size(width, 1920),
        devicePixelRatio: pixelRatio,
      ),
      child: Directionality(
        textDirection: TextDirection.ltr,
        child: child,
      ),
    );
  }
}