import 'dart:convert';

import 'package:ai/ai.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:markdown/markdown.dart' as markdown;
import 'package:shared_data/shared_data.dart';
import 'package:shared_service/shared_service.dart';
import 'package:shared_ui/shared_ui.dart';

class AIAllConversationItemView extends StatefulWidget {
  const AIAllConversationItemView({
    super.key,
    required this.viewModel,
    required this.onEditTapped,
    required this.onRead,
  });

  final AIGPTConversationResponse viewModel;
  final Function() onEditTapped;
  final Function() onRead;

  @override
  State<AIAllConversationItemView> createState() =>
      _AIAllConversationItemViewState();
}

class _AIAllConversationItemViewState extends State<AIAllConversationItemView>
    with AIGPTScreenNavigator {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        widget.onRead.call();

        final messages = List<AIGPTMessage>.from([]);

        final messageDisplaySize = ViewUtility.getTextSize(
          context,
          unwrap(widget.viewModel.query, ''),
          FontStyles.Regular16,
          ViewUtility.getWidthScreen(context) - (16 * 2) - 42 - (12 * 2),
        );
        final messageDisplayHeight =
            messageDisplaySize.height + (12 * 2) + (12 + 24);

        debugPrint('messageDisplaySize  $messageDisplayHeight');

        messages
          ..add(AIGPTMessage.init(
            side: AIGPTChatSide.ASK,
            message: unwrap(widget.viewModel.query, ''),
            askMessage: unwrap(widget.viewModel.query, ''),
            progressType: AIGPTMessageProgressType.DONE,
            askMessageHeightView: messageDisplayHeight,
            suggestionMetadata: null,
            financialStatements: null,
          ));
        if (StringUtility.isNotEmpty(widget.viewModel.answer)) {
          var companyDatas = List<AIGPTFinancialStatementsResponse>.from(
                  unwrap(widget.viewModel.companyFact, []))
              .where((ele) => ele.data != null && ele.data!.isNotEmpty)
              .toList();

          if (widget.viewModel.tickers != null &&
              widget.viewModel.tickers!.length > 1) {
            companyDatas
              ..addAll(widget.viewModel.tickers!.where((ticker) {
                return companyDatas.every((ele) =>
                    !StringUtility.isEqualIgnoreCase(ele.ticker, ticker));
              }).map((ticker) {
                return AIGPTFinancialStatementsResponse(
                  companyInfo: null,
                  ticker: ticker,
                  data: null,
                  queryId: widget.viewModel.id,
                );
              }));
          }
          messages
            ..add(
              AIGPTMessage.init(
                side: AIGPTChatSide.ANSWER,
                message: widget.viewModel.answer!,
                askMessage: unwrap(widget.viewModel.query, ''),
                progressType: AIGPTMessageProgressType.DONE,
                askMessageHeightView: messageDisplayHeight,
                suggestionMetadata: widget.viewModel.metadata,
                financialStatements:
                    ListUtility.isEmpty(companyDatas) ? null : companyDatas,
              ),
            );
        } else {
          messages
            ..add(
              AIGPTMessage.init(
                side: AIGPTChatSide.RETRY,
                message: '',
                askMessage: unwrap(widget.viewModel.query, ''),
                progressType: AIGPTMessageProgressType.ERROR,
                askMessageHeightView: messageDisplayHeight,
                suggestionMetadata: null,
                financialStatements: null,
              ),
            );
        }

        var mode = 'NONE';
        if (widget.viewModel.type == AIData.CONVERSATION_CALENDAR) {
          mode = 'ECONOMIC_CALENDAR';
        } else if (widget.viewModel.type == AIData.CONVERSATION_ANALYSING) {
          mode = 'ANALYSIS';
        } else if (widget.viewModel.type == AIData.CONVERSATION_SCREENER) {
          mode = 'STOCK_SCREENER';
        } else if (widget.viewModel.type == AIData.CONVERSATION_FILLINGS) {
          mode = 'SEC_FILINGS';
        }

        navigateToAIGPTScreen(
          context,
          AIGPTScreenParams(
            messageGroupEntity: AIGPTMessageGroupEntity(
              id: NumUtility.parseIntWithNull(widget.viewModel.id),
              userId: '',
              title: unwrap(widget.viewModel.query, ''),
              mode: mode,
              messageGroupJson:
                  jsonEncode(messages.map((e) => e.toJson()).toList()),
              updatedAt: DateTimeUtility.convertStringToDateTime(
                      widget.viewModel.timestamp) ??
                  DateTime.now(),
            ),
          ),
        );
      },
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(height: 12),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: Text(
                  unwrap(widget.viewModel.query, ''),
                  style: FontStyles.Medium18_NoLineHeight.apply(
                      color: ColorTheme.neutral2),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
              // 显示未读红点标记
              if (widget.viewModel.read != true) ...[
                // 如果read为null或false
                const SizedBox(width: 4),
                Container(
                  width: 8,
                  height: 8,
                  decoration: const BoxDecoration(
                    color: ColorTheme.gFB465D, // 使用红色
                    shape: BoxShape.circle,
                  ),
                ),
              ],
            ],
          ),
          if (StringUtility.isNotEmpty(widget.viewModel.answer)) ...[
            const SizedBox(height: 4),
            Text(
              HtmlUtility.getTextWithoutStyle(markdown
                  .markdownToHtml(unwrap(widget.viewModel.answer, ''))
                  .replaceAll('\r', '')
                  .replaceAll('\n', '')),
              style: FontStyles.Regular15.apply(color: ColorTheme.neutral2),
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ],
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: Text(
                  DateTimeUtility.convertDateTimeToString(
                      DateTimeUtility.convertToDateTime(
                          widget.viewModel.timestamp),
                      YYYY_DOT_MM_DOT_DD),
                  style: FontStyles.Regular12_NoLineHeight.apply(
                      color: ColorTheme.g868FA0),
                ),
              ),
              const SizedBox(width: 4),
              GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: widget.onEditTapped,
                child: SvgPicture.asset(
                  getCommunityIconPath('three_dots.svg'),
                  width: 16,
                  height: 16,
                  colorFilter: const ColorFilter.mode(
                    ColorTheme.neutral2,
                    BlendMode.srcIn,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
        ],
      ),
    );
  }
}
