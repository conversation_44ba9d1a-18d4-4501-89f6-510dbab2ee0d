# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

default_platform(:ios)

platform :ios do

  desc "Deploy a ipa version to the App Center"
  lane :deployIPAToAppCenter do |options|
   puts "Options #{options}"
   modify_app_version(type: 'adhoc', appVersion: options[:APP_VERSION])
   # modify app version based on firebase
   latest_release = firebase_app_distribution_get_latest_release(
     app: "#{ENV['FIREBASE_APP_ID']}",
     service_credentials_file: "#{ENV['FIREBASE_DISTRIBUTION_APP_JSON_KEY']}"
   )
   puts "latest_release #{latest_release}"
   increment_build_number(
     build_number: latest_release[:buildVersion].to_i + 1,
   )
   build_adhoc(type: 'adhoc')
   appcenter_upload(
     api_token: "#{ENV['APPCENTER_KEY']}",
     owner_name: "#{ENV['APPCENTER_OWNER_NAME']}",
     owner_type: "user", # Default is user - set to organization for appcenter organizations
     app_name: "#{ENV['APPCENTER_APP_NAME']}",
     file: "#{ENV['IPA_PATH']}/#{ENV['IPA_FILE_NAME']}-#{ENV['ENVIRONMENT']}-adhoc.ipa",
     notify_testers: true, # Set to false if you don't want to notify testers of your new release (default: `false`)
     destinations: "Collaborators,Testers"
   )
   firebase_app_distribution(
     app: "#{ENV['FIREBASE_APP_ID']}",
     groups: "testers",
     googleservice_info_plist_path: "#{ENV['FIREBASE_GOOGLE_SERVICE_JSON_KEY']}",
     service_credentials_file: "#{ENV['FIREBASE_DISTRIBUTION_APP_JSON_KEY']}",
     release_notes: "New app version",
     ipa_path: "#{ENV['IPA_PATH']}/#{ENV['IPA_FILE_NAME']}-#{ENV['ENVIRONMENT']}-adhoc.ipa",
     debug: true
   )
  end

  lane :build_adhoc do |options|
   gym(
     scheme: "#{ENV['SCHEME']}",
     clean: true,
     export_method: "ad-hoc",
     output_directory: "#{ENV['IPA_PATH']}",
     output_name: "#{ENV['IPA_FILE_NAME']}-#{ENV['ENVIRONMENT']}-adhoc.ipa",
     clean: true,
     export_options: {
        iCloudContainerEnvironment: "#{ENV['ICLOUD_CONTAINER_ADHOC']}",
        manageAppVersionAndBuildNumber: false,
        signingStyle: "manual",
        provisioningProfiles: {
          "#{ENV['BUNDLE_ID']}#{ENV['SUFFIX_BUNDLE_ID']}" => "#{ENV['BUNDLE_ID']}#{ENV['SUFFIX_BUNDLE_ID']} Pipeline-#{options[:type]}",
          "#{ENV['BUNDLE_ID']}#{ENV['SUFFIX_BUNDLE_ID']}.ImageNotification" => "#{ENV['BUNDLE_ID']}#{ENV['SUFFIX_BUNDLE_ID']}.ImageNotification Pipeline-#{options[:type]}",
          "#{ENV['BUNDLE_ID']}#{ENV['SUFFIX_BUNDLE_ID']}.SharingIntent" => "#{ENV['BUNDLE_ID']}#{ENV['SUFFIX_BUNDLE_ID']}.SharingIntent Pipeline-#{options[:type]}"
        },
        manageAppVersionAndBuildNumber: false
     }
   )
  end

  desc "Deploy a ipa version to the TestFlight"
  lane :deployIPAToTestFlight do |options|
    puts "Options #{options}"
    modify_app_version(type: 'appstore', appVersion: options[:APP_VERSION])
    build_testflight(type: 'appstore')
    api_key = get_api_key
    upload_to_testflight(
       api_key: api_key,
       ipa: "#{ENV['IPA_PATH']}/#{ENV['IPA_FILE_NAME']}-#{ENV['ENVIRONMENT']}-testflight.ipa",
       skip_submission: true,
       skip_waiting_for_build_processing: true
    )
  end

  lane :build_testflight do |options|
    gym(
       scheme: "#{ENV['SCHEME']}",
       clean: true,
       export_method: "app-store",
       output_directory: "#{ENV['IPA_PATH']}",
       output_name: "#{ENV['IPA_FILE_NAME']}-#{ENV['ENVIRONMENT']}-testflight.ipa",
       clean: true,
       export_options: {
          iCloudContainerEnvironment: "#{ENV['ICLOUD_CONTAINER']}",
          manageAppVersionAndBuildNumber: false,
          signingStyle: "manual",
          provisioningProfiles: {
            "#{ENV['BUNDLE_ID']}#{ENV['SUFFIX_BUNDLE_ID']}" => "#{ENV['BUNDLE_ID']}#{ENV['SUFFIX_BUNDLE_ID']} Pipeline-#{options[:type]}",
            "#{ENV['BUNDLE_ID']}#{ENV['SUFFIX_BUNDLE_ID']}.ImageNotification" => "#{ENV['BUNDLE_ID']}#{ENV['SUFFIX_BUNDLE_ID']}.ImageNotification Pipeline-#{options[:type]}",
            "#{ENV['BUNDLE_ID']}#{ENV['SUFFIX_BUNDLE_ID']}.SharingIntent" => "#{ENV['BUNDLE_ID']}#{ENV['SUFFIX_BUNDLE_ID']}.SharingIntent Pipeline-#{options[:type]}"
          },
          manageAppVersionAndBuildNumber: false
       }
    )
  end

  lane :modify_app_version do |options|
     puts "appVersion #{options[:appVersion]}"
     download_certificates(options)
     api_key = get_api_key
     previous_build_number = latest_testflight_build_number(
       app_identifier: "#{ENV['BUNDLE_ID']}#{ENV['SUFFIX_BUNDLE_ID']}",
       api_key: api_key
     )
     puts "previous_build_number: #{previous_build_number}"

     current_build_number = ((previous_build_number.is_a? String) ? previous_build_number.to_i : previous_build_number) + 1
     puts "current_build_number: #{current_build_number}"

     increment_build_number(
       build_number: current_build_number,
     )
     increment_version_number(
       version_number: "#{options[:appVersion]}"
     )
     register_devices(
        devices_file: "./fastlane/devices.txt",
        api_key: api_key
     )
   end

   lane :download_certificates do |options|
      puts "type #{options[:type]}"
      api_key = get_api_key
      # sign for app
      with_retry(retries: 5, delay: 5) do
          match(
            type: options[:type],
            git_url: "#{ENV['MATCH_GIT_URL']}",
            api_key: api_key,
            output_path: "#{ENV['CERTIFICATE_PATH']}",
            username: "#{ENV['DEVELOPER_NAME']}",
            team_id: "#{ENV['TEAM_ID']}",
            team_name: "#{ENV['TEAM_NAME']}",
            app_identifier: "#{ENV['BUNDLE_ID']}#{ENV['SUFFIX_BUNDLE_ID']}",
            generate_apple_certs: true,
            profile_name: "#{ENV['BUNDLE_ID']}#{ENV['SUFFIX_BUNDLE_ID']} Pipeline-#{options[:type]}",
            readonly: true
          )
      end
      # sign for image notification
      with_retry(retries: 5, delay: 5) do
          match(
            type: options[:type],
            git_url: "#{ENV['MATCH_GIT_URL']}",
            api_key: api_key,
            output_path: "#{ENV['CERTIFICATE_PATH']}",
            username: "#{ENV['DEVELOPER_NAME']}",
            team_id: "#{ENV['TEAM_ID']}",
            team_name: "#{ENV['TEAM_NAME']}",
            app_identifier: "#{ENV['BUNDLE_ID']}#{ENV['SUFFIX_BUNDLE_ID']}.ImageNotification",
            generate_apple_certs: true,
            profile_name: "#{ENV['BUNDLE_ID']}#{ENV['SUFFIX_BUNDLE_ID']}.ImageNotification Pipeline-#{options[:type]}",
            readonly: true
          )
      end
      # sign for sharing intent
      with_retry(retries: 5, delay: 5) do
          match(
             type: options[:type],
             git_url: "#{ENV['MATCH_GIT_URL']}",
             api_key: api_key,
             output_path: "#{ENV['CERTIFICATE_PATH']}",
             username: "#{ENV['DEVELOPER_NAME']}",
             team_id: "#{ENV['TEAM_ID']}",
             team_name: "#{ENV['TEAM_NAME']}",
             app_identifier: "#{ENV['BUNDLE_ID']}#{ENV['SUFFIX_BUNDLE_ID']}.SharingIntent",
             generate_apple_certs: true,
             profile_name: "#{ENV['BUNDLE_ID']}#{ENV['SUFFIX_BUNDLE_ID']}.SharingIntent Pipeline-#{options[:type]}",
             readonly: true
           )
      end
   end

   lane :get_api_key do
     api_key = {
        key_id: "#{ENV['APP_STORE_CONNECT_KEY_ID']}",
        issuer_id: "#{ENV['APP_STORE_CONNECT_USER_ID']}",
        key: File.binread(File.expand_path("#{ENV['APP_STORE_CONNECT_KEY_FILEPATH']}")),
        is_key_content_base64: false,
        duration: 1200,
        in_house: false
     }
     puts "api_key #{api_key}"
     api_key
   end

   def with_retry(retries: 5, delay: 5)
     attempts = 0
     begin
       attempts += 1
       yield  # Execute the passed block
     rescue => e
       if attempts < retries
         puts "Operation failed, retrying in #{delay} seconds... (Attempt #{attempts}/#{retries})"
         sleep(delay)
         retry
       else
         puts "Operation failed after #{retries} attempts. Exiting."
         raise e
       end
     end
   end

end
