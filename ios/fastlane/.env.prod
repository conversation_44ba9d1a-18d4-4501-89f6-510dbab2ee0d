ENVIRONMENT=prod
SCHEME=prod
BUNDLE_ID="com.addxgo"
SUFFIX_BUNDLE_ID=""
APP_VERSION="1.0.0"
TEAM_NAME="ADDX Digital Pte. Ltd."
TEAM_ID="356APC8RAP"
DEVELOPER_NAME="<EMAIL>"

# APP CENTER
APPCENTER_KEY=cb9144140037a566c50dded928b00a20ba2589d8
APPCENTER_OWNER_NAME=hung-addxgo.io
APPCENTER_APP_NAME=ADDX-Go-Wallet-iOS
SUPPLY_UPLOAD_MAX_RETRIES=5
IPA_PATH="../build/app/outputs/ipa"
IPA_FILE_NAME="addx-go-wallet"

# FASTLANE
FASTLANE_XCODEBUILD_SETTINGS_TIMEOUT=120

# ICLOUD CONTAINER
ICLOUD_CONTAINER_ADHOC=Production
ICLOUD_CONTAINER=Production

# APP STORE CONNECT
APP_STORE_CONNECT_KEY_ID="8Z49N49536"
APP_STORE_CONNECT_USER_ID="2c411fe2-15b8-45a6-a4f2-1205694e3443"
APP_STORE_CONNECT_KEY_FILEPATH="../AuthKey/ADDX_Go_Wallet_UploadAppStore_8Z49N49536.p8"

# CERTIFICATE
CERTIFICATE_PATH="./certificates/"

# GIT
MATCH_GIT_URL="https://addxgo_hung:<EMAIL>/addxgo/mobile/ios-certificate.git"

MATCH_PASSWORD="password"
MATCH_KEYCHAIN_PASSWORD="password"

FIREBASE_APP_ID="1:562260331334:ios:c0c3e7c9f2bbea693c7347"
FIREBASE_DISTRIBUTION_APP_JSON_KEY="config/prod/firebase-distribution-app-credential.json"
FIREBASE_GOOGLE_SERVICE_JSON_KEY="config/prod/GoogleService-Info.plist"

MATCH_FORCE_LEGACY_ENCRYPTION=true