ENVIRONMENT=qa
SCHEME=qa
BUNDLE_ID="com.addxgo.wallet"
SUFFIX_BUNDLE_ID=".qa"
APP_VERSION="1.0.0"
TEAM_NAME="ADDX Digital Pte. Ltd."
TEAM_ID="356APC8RAP"
DEVELOPER_NAME="<EMAIL>"

# APP CENTER
APPCENTER_KEY=76204e302bc43110dfea7efc2f6892ec636e2523
APPCENTER_OWNER_NAME=hung-addxgo.io
APPCENTER_APP_NAME=ADDX-Go-Wallet-QA-iOS
SUPPLY_UPLOAD_MAX_RETRIES=5
IPA_PATH="../build/app/outputs/ipa"
IPA_FILE_NAME="addx-go-wallet"

# FASTLANE
FASTLANE_XCODEBUILD_SETTINGS_TIMEOUT=120

# ICLOUD CONTAINER
ICLOUD_CONTAINER_ADHOC=Production
ICLOUD_CONTAINER=Production

# APP STORE CONNECT
APP_STORE_CONNECT_KEY_ID="8Z49N49536"
APP_STORE_CONNECT_USER_ID="2c411fe2-15b8-45a6-a4f2-1205694e3443"
APP_STORE_CONNECT_KEY_FILEPATH="../AuthKey/ADDX_Go_Wallet_UploadAppStore_8Z49N49536.p8"

# CERTIFICATE
CERTIFICATE_PATH="./certificates/"

# GIT
MATCH_GIT_URL="https://addxgo_hung:<EMAIL>/addxgo/mobile/ios-certificate.git"

MATCH_PASSWORD="password"
MATCH_KEYCHAIN_PASSWORD="password"

FIREBASE_APP_ID="1:979486348420:ios:b7c57f0ff9e592c4ba1ed1"
FIREBASE_DISTRIBUTION_APP_JSON_KEY="config/qa/firebase-distribution-app-credential.json"
FIREBASE_GOOGLE_SERVICE_JSON_KEY="config/qa/GoogleService-Info.plist"

MATCH_FORCE_LEGACY_ENCRYPTION=true